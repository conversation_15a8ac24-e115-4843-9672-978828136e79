syntax = "proto3";

package missionplanning;

// 基础数据类型
message Coordinate {
  double latitude = 1;
  double longitude = 2;
  double altitude = 3;
}

message Timestamp {
  int64 seconds = 1;
  int32 nanos = 2;
}

// 无人机相关
message DroneSpec {
  string id = 1;
  string name = 2;
  string model = 3;
  double max_speed = 4;        // m/s
  double max_altitude = 5;     // meters
  double battery_life = 6;     // seconds
  double payload_capacity = 7; // kg
  repeated string capabilities = 8;
}

message DroneStatus {
  string drone_id = 1;
  Coordinate position = 2;
  double speed = 3;
  double heading = 4;
  double battery_level = 5;    // 0.0 - 1.0
  string status = 6;           // IDLE, FLYING, CHARGING, MAINTENANCE
  Timestamp last_update = 7;
}

// 任务相关
message Target {
  string id = 1;
  string batch_id = 2;
  string type = 3;             // BUILDING, VEHICLE, PERSON, etc.
  Coordinate position = 4;
  double confidence = 5;       // 0.0 - 1.0
  string description = 6;
  Timestamp detected_at = 7;
}

message Waypoint {
  string id = 1;
  Coordinate position = 2;
  double speed = 3;
  double altitude = 4;
  string action = 5;           // SURVEY, PHOTO, HOVER, etc.
  int32 duration = 6;          // seconds
  map<string, string> metadata = 7;
}

message FlightPath {
  string id = 1;
  string drone_id = 2;
  repeated Waypoint waypoints = 3;
  double total_distance = 4;   // meters
  int32 estimated_duration = 5; // seconds
  string status = 6;           // PLANNED, ACTIVE, COMPLETED, CANCELLED
  Timestamp created_at = 7;
  Timestamp updated_at = 8;
}

message Mission {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated string target_ids = 4;
  repeated FlightPath flight_paths = 5;
  string status = 6;           // PLANNING, ACTIVE, COMPLETED, CANCELLED
  Coordinate area_center = 7;
  double area_radius = 8;      // meters
  Timestamp created_at = 9;
  Timestamp started_at = 10;
  Timestamp completed_at = 11;
  map<string, string> parameters = 12;
}

// AI 规划相关
message PlanningRequest {
  string user_id = 1;
  string conversation_id = 2;
  string message = 3;
  repeated Target targets = 4;
  repeated DroneSpec available_drones = 5;
  Coordinate area_center = 6;
  double area_radius = 7;
  map<string, string> constraints = 8;
  Timestamp timestamp = 9;
}

message PlanningResponse {
  string conversation_id = 1;
  string response_text = 2;
  Mission suggested_mission = 3;
  repeated string reasoning_steps = 4;
  double confidence_score = 5;
  repeated string alternatives = 6;
  Timestamp timestamp = 7;
}

// 可视化相关
message VisualizationRequest {
  string session_id = 1;
  string type = 2;             // MISSION, REAL_TIME, HISTORICAL
  repeated Mission missions = 3;
  repeated DroneStatus drone_statuses = 4;
  repeated Target targets = 5;
  map<string, string> view_options = 6;
}

message VisualizationUpdate {
  string session_id = 1;
  string update_type = 2;      // DRONE_POSITION, NEW_TARGET, PATH_UPDATE
  oneof update_data {
    DroneStatus drone_status = 3;
    Target target = 4;
    FlightPath flight_path = 5;
  }
  Timestamp timestamp = 6;
}

// WebSocket 消息
message WebSocketMessage {
  string type = 1;             // CHAT, MISSION_UPDATE, DRONE_STATUS, etc.
  string session_id = 2;
  oneof payload {
    PlanningRequest planning_request = 3;
    PlanningResponse planning_response = 4;
    Mission mission = 5;
    DroneStatus drone_status = 6;
    Target target = 7;
    VisualizationUpdate viz_update = 8;
  }
  Timestamp timestamp = 9;
}

// gRPC 服务定义
service MissionPlanningService {
  // AI 规划服务
  rpc PlanMission(PlanningRequest) returns (PlanningResponse);
  rpc StreamPlanning(stream PlanningRequest) returns (stream PlanningResponse);
  
  // 任务管理
  rpc CreateMission(Mission) returns (Mission);
  rpc GetMission(GetMissionRequest) returns (Mission);
  rpc UpdateMission(Mission) returns (Mission);
  rpc DeleteMission(DeleteMissionRequest) returns (DeleteMissionResponse);
  rpc ListMissions(ListMissionsRequest) returns (ListMissionsResponse);
  
  // 无人机管理
  rpc RegisterDrone(DroneSpec) returns (DroneSpec);
  rpc UpdateDroneStatus(DroneStatus) returns (DroneStatus);
  rpc GetDroneStatus(GetDroneStatusRequest) returns (DroneStatus);
  rpc ListDrones(ListDronesRequest) returns (ListDronesResponse);
}

service VisualizationService {
  // 可视化服务
  rpc InitializeVisualization(VisualizationRequest) returns (VisualizationResponse);
  rpc UpdateVisualization(VisualizationUpdate) returns (VisualizationResponse);
  rpc StreamVisualization(stream VisualizationRequest) returns (stream VisualizationUpdate);
}

// 辅助消息类型
message GetMissionRequest {
  string mission_id = 1;
}

message DeleteMissionRequest {
  string mission_id = 1;
}

message DeleteMissionResponse {
  bool success = 1;
  string message = 2;
}

message ListMissionsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string status_filter = 3;
}

message ListMissionsResponse {
  repeated Mission missions = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message GetDroneStatusRequest {
  string drone_id = 1;
}

message ListDronesRequest {
  string status_filter = 1;
}

message ListDronesResponse {
  repeated DroneStatus drones = 1;
  int32 total_count = 2;
}

message VisualizationResponse {
  bool success = 1;
  string message = 2;
  string session_id = 3;
}
