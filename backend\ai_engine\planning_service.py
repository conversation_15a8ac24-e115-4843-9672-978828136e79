"""
AI规划服务
"""
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger

from app.models import (
    PlanningRequest, PlanningResponse, Mission, Target, DroneSpec,
    FlightPath, Waypoint, Coordinate
)
from ai_engine.qwen_client import QwenClient
from ai_engine.conversation_manager import ConversationManager


class PlanningService:
    """AI规划服务"""
    
    def __init__(self, qwen_client: QwenClient, conversation_manager: ConversationManager):
        self.qwen_client = qwen_client
        self.conversation_manager = conversation_manager
        
    async def process_planning_request(
        self,
        request: PlanningRequest,
        db_session=None
    ) -> PlanningResponse:
        """处理规划请求"""
        try:
            # 记录用户消息
            await self.conversation_manager.add_message(
                conversation_id=request.conversation_id,
                role="user",
                content=request.message,
                metadata={
                    "user_id": request.user_id,
                    "targets_count": len(request.targets),
                    "drones_count": len(request.available_drones),
                    "has_area": bool(request.area_center and request.area_radius)
                },
                db=db_session
            )
            
            # 获取对话历史
            conversation_history = await self.conversation_manager.format_messages_for_ai(
                conversation_id=request.conversation_id,
                db=db_session
            )
            
            # 调用AI模型进行规划
            response = await self.qwen_client.plan_mission(request)
            
            # 记录AI响应
            await self.conversation_manager.add_message(
                conversation_id=request.conversation_id,
                role="assistant",
                content=response.response_text,
                metadata={
                    "confidence_score": response.confidence_score,
                    "has_mission": bool(response.suggested_mission),
                    "reasoning_steps_count": len(response.reasoning_steps),
                    "alternatives_count": len(response.alternatives)
                },
                db=db_session
            )
            
            logger.info(f"处理规划请求完成: {request.conversation_id}")
            return response
            
        except Exception as e:
            logger.error(f"处理规划请求失败: {e}")
            
            # 记录错误响应
            error_response = PlanningResponse(
                conversation_id=request.conversation_id,
                response_text=f"抱歉，处理您的请求时出现错误：{str(e)}",
                suggested_mission=None,
                reasoning_steps=["错误：无法完成规划"],
                confidence_score=0.0,
                alternatives=[]
            )
            
            await self.conversation_manager.add_message(
                conversation_id=request.conversation_id,
                role="assistant",
                content=error_response.response_text,
                metadata={"error": True, "error_message": str(e)},
                db=db_session
            )
            
            return error_response
            
    async def generate_detailed_mission(
        self,
        request: PlanningRequest,
        db_session=None
    ) -> Optional[Mission]:
        """生成详细的任务方案"""
        try:
            # 首先进行基础规划
            planning_response = await self.process_planning_request(request, db_session)
            
            if not planning_response.suggested_mission:
                return None
                
            mission = planning_response.suggested_mission
            
            # 生成详细的飞行路径
            flight_paths = await self._generate_flight_paths(
                mission=mission,
                targets=request.targets,
                drones=request.available_drones,
                constraints=request.constraints
            )
            
            mission.flight_paths = flight_paths
            
            # 计算任务统计信息
            total_distance = sum(fp.total_distance for fp in flight_paths)
            total_duration = max(fp.estimated_duration for fp in flight_paths) if flight_paths else 0
            
            mission.parameters.update({
                "total_distance": total_distance,
                "total_duration": total_duration,
                "flight_paths_count": len(flight_paths),
                "waypoints_count": sum(len(fp.waypoints) for fp in flight_paths)
            })
            
            logger.info(f"生成详细任务方案: {mission.id}")
            return mission
            
        except Exception as e:
            logger.error(f"生成详细任务方案失败: {e}")
            return None
            
    async def _generate_flight_paths(
        self,
        mission: Mission,
        targets: List[Target],
        drones: List[DroneSpec],
        constraints: Dict[str, Any]
    ) -> List[FlightPath]:
        """生成飞行路径"""
        try:
            flight_paths = []
            
            if not targets or not drones:
                return flight_paths
                
            # 简单的目标分配策略：平均分配
            targets_per_drone = len(targets) // len(drones)
            remaining_targets = len(targets) % len(drones)
            
            target_index = 0
            for i, drone in enumerate(drones):
                # 计算这架无人机分配的目标数量
                drone_target_count = targets_per_drone
                if i < remaining_targets:
                    drone_target_count += 1
                    
                if drone_target_count == 0:
                    continue
                    
                # 分配目标
                drone_targets = targets[target_index:target_index + drone_target_count]
                target_index += drone_target_count
                
                # 生成航点
                waypoints = await self._generate_waypoints(
                    drone=drone,
                    targets=drone_targets,
                    mission_center=mission.area_center,
                    constraints=constraints
                )
                
                # 计算路径统计
                total_distance = self._calculate_path_distance(waypoints)
                estimated_duration = self._estimate_flight_duration(
                    waypoints, drone.max_speed
                )
                
                flight_path = FlightPath(
                    drone_id=drone.id,
                    waypoints=waypoints,
                    total_distance=total_distance,
                    estimated_duration=estimated_duration
                )
                
                flight_paths.append(flight_path)
                
            return flight_paths
            
        except Exception as e:
            logger.error(f"生成飞行路径失败: {e}")
            return []
            
    async def _generate_waypoints(
        self,
        drone: DroneSpec,
        targets: List[Target],
        mission_center: Coordinate,
        constraints: Dict[str, Any]
    ) -> List[Waypoint]:
        """生成航点"""
        try:
            waypoints = []
            
            # 起始点（任务中心）
            start_waypoint = Waypoint(
                position=mission_center,
                speed=drone.max_speed * 0.7,  # 70%最大速度
                altitude=constraints.get("default_altitude", 100.0),
                action="TAKEOFF",
                duration=30  # 起飞准备时间
            )
            waypoints.append(start_waypoint)
            
            # 目标点
            for target in targets:
                target_waypoint = Waypoint(
                    position=target.position,
                    speed=drone.max_speed * 0.5,  # 50%速度进行侦察
                    altitude=constraints.get("survey_altitude", 80.0),
                    action="SURVEY",
                    duration=constraints.get("survey_duration", 60),  # 侦察时间
                    metadata={
                        "target_id": target.id,
                        "target_type": target.type,
                        "confidence": target.confidence
                    }
                )
                waypoints.append(target_waypoint)
                
            # 返回点
            return_waypoint = Waypoint(
                position=mission_center,
                speed=drone.max_speed * 0.8,
                altitude=constraints.get("return_altitude", 120.0),
                action="LANDING",
                duration=60  # 降落时间
            )
            waypoints.append(return_waypoint)
            
            return waypoints
            
        except Exception as e:
            logger.error(f"生成航点失败: {e}")
            return []
            
    def _calculate_path_distance(self, waypoints: List[Waypoint]) -> float:
        """计算路径总距离"""
        try:
            total_distance = 0.0
            
            for i in range(1, len(waypoints)):
                prev_point = waypoints[i-1].position
                curr_point = waypoints[i].position
                
                # 简单的欧几里得距离计算（实际应该使用地理距离）
                lat_diff = curr_point.latitude - prev_point.latitude
                lon_diff = curr_point.longitude - prev_point.longitude
                alt_diff = curr_point.altitude - prev_point.altitude
                
                # 转换为米（粗略计算）
                lat_meters = lat_diff * 111000  # 1度纬度约111km
                lon_meters = lon_diff * 111000 * abs(prev_point.latitude / 90)  # 经度随纬度变化
                alt_meters = alt_diff
                
                distance = (lat_meters**2 + lon_meters**2 + alt_meters**2)**0.5
                total_distance += distance
                
            return total_distance
            
        except Exception as e:
            logger.error(f"计算路径距离失败: {e}")
            return 0.0
            
    def _estimate_flight_duration(
        self, 
        waypoints: List[Waypoint], 
        max_speed: float
    ) -> int:
        """估算飞行时间（秒）"""
        try:
            total_time = 0
            
            # 飞行时间
            total_distance = self._calculate_path_distance(waypoints)
            avg_speed = max_speed * 0.6  # 平均速度为最大速度的60%
            flight_time = total_distance / avg_speed if avg_speed > 0 else 0
            
            # 任务时间
            task_time = sum(wp.duration for wp in waypoints)
            
            total_time = flight_time + task_time
            
            return int(total_time)
            
        except Exception as e:
            logger.error(f"估算飞行时间失败: {e}")
            return 0
            
    async def optimize_mission_paths(
        self,
        mission: Mission,
        algorithm: str = "genetic"
    ) -> Mission:
        """优化任务路径"""
        try:
            # TODO: 实现具体的路径优化算法
            # 这里先返回原始任务
            
            logger.info(f"路径优化完成: {mission.id}, 算法: {algorithm}")
            return mission
            
        except Exception as e:
            logger.error(f"路径优化失败: {e}")
            return mission
            
    async def validate_mission_feasibility(
        self,
        mission: Mission,
        drones: List[DroneSpec]
    ) -> Dict[str, Any]:
        """验证任务可行性"""
        try:
            validation_result = {
                "is_feasible": True,
                "warnings": [],
                "errors": [],
                "recommendations": []
            }
            
            # 检查无人机数量
            if len(mission.flight_paths) > len(drones):
                validation_result["errors"].append("分配的飞行路径数量超过可用无人机数量")
                validation_result["is_feasible"] = False
                
            # 检查每个飞行路径
            for flight_path in mission.flight_paths:
                drone = next((d for d in drones if d.id == flight_path.drone_id), None)
                if not drone:
                    validation_result["errors"].append(f"无人机 {flight_path.drone_id} 不存在")
                    validation_result["is_feasible"] = False
                    continue
                    
                # 检查电池续航
                if flight_path.estimated_duration > drone.battery_life:
                    validation_result["warnings"].append(
                        f"无人机 {drone.name} 的电池可能不足以完成任务"
                    )
                    validation_result["recommendations"].append(
                        f"建议为无人机 {drone.name} 增加充电点或缩短航线"
                    )
                    
                # 检查飞行高度
                max_altitude = max(wp.altitude for wp in flight_path.waypoints)
                if max_altitude > drone.max_altitude:
                    validation_result["errors"].append(
                        f"无人机 {drone.name} 的最大飞行高度不足"
                    )
                    validation_result["is_feasible"] = False
                    
            return validation_result
            
        except Exception as e:
            logger.error(f"验证任务可行性失败: {e}")
            return {
                "is_feasible": False,
                "warnings": [],
                "errors": [f"验证过程出错: {str(e)}"],
                "recommendations": []
            }
