"""
目标管理路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from loguru import logger

from app.models import Target, TargetDB
from database.connection import get_pg_session

router = APIRouter()


@router.post("/", response_model=Target)
async def create_target(
    target: Target,
    db: AsyncSession = Depends(get_pg_session)
):
    """创建目标"""
    try:
        # 转换为数据库模型
        target_db = TargetDB(
            id=target.id,
            batch_id=target.batch_id,
            type=target.type,
            latitude=target.position.latitude,
            longitude=target.position.longitude,
            altitude=target.position.altitude,
            confidence=target.confidence,
            description=target.description,
            detected_at=target.detected_at
        )
        
        db.add(target_db)
        await db.commit()
        await db.refresh(target_db)
        
        logger.info(f"创建目标成功: {target.id}")
        return target
        
    except Exception as e:
        logger.error(f"创建目标失败: {e}")
        raise HTTPException(status_code=500, detail="创建目标失败")


@router.post("/batch", response_model=List[Target])
async def create_targets_batch(
    targets: List[Target],
    db: AsyncSession = Depends(get_pg_session)
):
    """批量创建目标"""
    try:
        # 转换为数据库模型
        targets_db = []
        for target in targets:
            target_db = TargetDB(
                id=target.id,
                batch_id=target.batch_id,
                type=target.type,
                latitude=target.position.latitude,
                longitude=target.position.longitude,
                altitude=target.position.altitude,
                confidence=target.confidence,
                description=target.description,
                detected_at=target.detected_at
            )
            targets_db.append(target_db)
        
        db.add_all(targets_db)
        await db.commit()
        
        logger.info(f"批量创建目标成功: {len(targets)} 个目标")
        return targets
        
    except Exception as e:
        logger.error(f"批量创建目标失败: {e}")
        raise HTTPException(status_code=500, detail="批量创建目标失败")


@router.get("/", response_model=List[Target])
async def list_targets(
    batch_id: Optional[str] = Query(None, description="批次ID过滤"),
    target_type: Optional[str] = Query(None, description="目标类型过滤"),
    min_confidence: Optional[float] = Query(None, ge=0, le=1, description="最小置信度"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_pg_session)
):
    """获取目标列表"""
    try:
        query = select(TargetDB)
        
        # 应用过滤条件
        if batch_id:
            query = query.where(TargetDB.batch_id == batch_id)
        if target_type:
            query = query.where(TargetDB.type == target_type)
        if min_confidence is not None:
            query = query.where(TargetDB.confidence >= min_confidence)
            
        # 按检测时间倒序排列
        query = query.order_by(TargetDB.detected_at.desc())
        
        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        result = await db.execute(query)
        targets_db = result.scalars().all()
        
        # 转换为Pydantic模型
        targets = []
        for target_db in targets_db:
            target = Target(
                id=target_db.id,
                batch_id=target_db.batch_id,
                type=target_db.type,
                position={
                    "latitude": target_db.latitude,
                    "longitude": target_db.longitude,
                    "altitude": target_db.altitude
                },
                confidence=target_db.confidence,
                description=target_db.description,
                detected_at=target_db.detected_at
            )
            targets.append(target)
            
        return targets
        
    except Exception as e:
        logger.error(f"获取目标列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取目标列表失败")


@router.get("/{target_id}", response_model=Target)
async def get_target(
    target_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """获取目标详情"""
    try:
        query = select(TargetDB).where(TargetDB.id == target_id)
        result = await db.execute(query)
        target_db = result.scalar_one_or_none()
        
        if not target_db:
            raise HTTPException(status_code=404, detail="目标不存在")
            
        # 转换为Pydantic模型
        target = Target(
            id=target_db.id,
            batch_id=target_db.batch_id,
            type=target_db.type,
            position={
                "latitude": target_db.latitude,
                "longitude": target_db.longitude,
                "altitude": target_db.altitude
            },
            confidence=target_db.confidence,
            description=target_db.description,
            detected_at=target_db.detected_at
        )
        
        return target
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取目标详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取目标详情失败")


@router.delete("/{target_id}")
async def delete_target(
    target_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """删除目标"""
    try:
        # 检查目标是否存在
        query = select(TargetDB).where(TargetDB.id == target_id)
        result = await db.execute(query)
        existing_target = result.scalar_one_or_none()
        
        if not existing_target:
            raise HTTPException(status_code=404, detail="目标不存在")
            
        # 删除目标
        delete_query = delete(TargetDB).where(TargetDB.id == target_id)
        await db.execute(delete_query)
        await db.commit()
        
        logger.info(f"删除目标成功: {target_id}")
        return {"message": "目标删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除目标失败: {e}")
        raise HTTPException(status_code=500, detail="删除目标失败")


@router.delete("/batch/{batch_id}")
async def delete_targets_batch(
    batch_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """批量删除目标（按批次ID）"""
    try:
        # 删除指定批次的所有目标
        delete_query = delete(TargetDB).where(TargetDB.batch_id == batch_id)
        result = await db.execute(delete_query)
        deleted_count = result.rowcount
        await db.commit()
        
        logger.info(f"批量删除目标成功: 批次 {batch_id}, 删除 {deleted_count} 个目标")
        return {"message": f"批量删除成功，删除了 {deleted_count} 个目标"}
        
    except Exception as e:
        logger.error(f"批量删除目标失败: {e}")
        raise HTTPException(status_code=500, detail="批量删除目标失败")


@router.get("/stats/summary")
async def get_targets_summary(
    db: AsyncSession = Depends(get_pg_session)
):
    """获取目标统计摘要"""
    try:
        from sqlalchemy import func
        
        # 总目标数
        total_query = select(func.count(TargetDB.id))
        total_result = await db.execute(total_query)
        total_count = total_result.scalar()
        
        # 按类型统计
        type_query = select(
            TargetDB.type,
            func.count(TargetDB.id).label("count")
        ).group_by(TargetDB.type)
        type_result = await db.execute(type_query)
        type_stats = {row.type: row.count for row in type_result}
        
        # 按批次统计
        batch_query = select(
            TargetDB.batch_id,
            func.count(TargetDB.id).label("count")
        ).group_by(TargetDB.batch_id)
        batch_result = await db.execute(batch_query)
        batch_stats = {row.batch_id: row.count for row in batch_result}
        
        # 平均置信度
        confidence_query = select(func.avg(TargetDB.confidence))
        confidence_result = await db.execute(confidence_query)
        avg_confidence = confidence_result.scalar() or 0.0
        
        return {
            "total_targets": total_count,
            "by_type": type_stats,
            "by_batch": batch_stats,
            "average_confidence": round(avg_confidence, 3)
        }
        
    except Exception as e:
        logger.error(f"获取目标统计摘要失败: {e}")
        raise HTTPException(status_code=500, detail="获取目标统计摘要失败")
