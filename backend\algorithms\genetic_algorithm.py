"""
遗传算法实现
"""
import random
import copy
from typing import List, Tuple, Dict, Any
from loguru import logger

from algorithms.base import PathOptimizer, PathSolution
from app.models import Target, DroneSpec


class GeneticAlgorithm(PathOptimizer):
    """遗传算法路径优化器"""
    
    def __init__(
        self,
        targets: List[Target],
        drones: List[DroneSpec],
        constraints: Dict[str, Any],
        population_size: int = 100,
        mutation_rate: float = 0.1,
        crossover_rate: float = 0.8,
        elite_size: int = 20
    ):
        super().__init__(targets, drones, constraints)
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.elite_size = elite_size
        self.distance_matrix = self.calculate_distance_matrix()
        
    def optimize(self, max_iterations: int = 1000) -> PathSolution:
        """使用遗传算法优化路径"""
        try:
            logger.info(f"开始遗传算法优化: 目标数={len(self.targets)}, 无人机数={len(self.drones)}")
            
            # 初始化种群
            population = self._initialize_population()
            
            best_solution = None
            best_fitness = float('inf')
            
            for generation in range(max_iterations):
                # 评估种群
                fitness_scores = []
                for individual in population:
                    solution = self._decode_individual(individual)
                    fitness = self.evaluate_solution(solution)
                    fitness_scores.append(fitness)
                    
                    if fitness < best_fitness:
                        best_fitness = fitness
                        best_solution = solution
                        
                # 选择
                selected = self._selection(population, fitness_scores)
                
                # 交叉
                offspring = self._crossover(selected)
                
                # 变异
                offspring = self._mutation(offspring)
                
                # 更新种群
                population = self._update_population(population, offspring, fitness_scores)
                
                # 记录进度
                if generation % 100 == 0:
                    logger.debug(f"遗传算法第 {generation} 代，最佳适应度: {best_fitness:.2f}")
                    
            logger.info(f"遗传算法优化完成，最佳适应度: {best_fitness:.2f}")
            return best_solution
            
        except Exception as e:
            logger.error(f"遗传算法优化失败: {e}")
            return self._create_fallback_solution()
            
    def _initialize_population(self) -> List[List[int]]:
        """初始化种群"""
        population = []
        
        for _ in range(self.population_size):
            # 创建个体：目标分配给无人机
            individual = self._create_random_individual()
            population.append(individual)
            
        return population
        
    def _create_random_individual(self) -> List[int]:
        """创建随机个体"""
        # 个体编码：每个目标分配给哪个无人机
        individual = []
        for _ in range(len(self.targets)):
            drone_id = random.randint(0, len(self.drones) - 1)
            individual.append(drone_id)
            
        return individual
        
    def _decode_individual(self, individual: List[int]) -> PathSolution:
        """解码个体为路径解决方案"""
        # 按无人机分组目标
        drone_targets = [[] for _ in range(len(self.drones))]
        
        for target_idx, drone_idx in enumerate(individual):
            drone_targets[drone_idx].append(target_idx)
            
        # 为每个无人机优化路径顺序（使用最近邻算法）
        paths = []
        for drone_idx, targets in enumerate(drone_targets):
            if targets:
                optimized_path = self._optimize_path_order(targets)
                paths.append(optimized_path)
            else:
                paths.append([])
                
        # 计算路径距离
        path_distances = []
        for path in paths:
            distance = self.calculate_path_distance(path, self.distance_matrix)
            path_distances.append(distance)
            
        total_distance = sum(path_distances)
        max_distance = max(path_distances) if path_distances else 0
        
        solution = PathSolution(
            paths=paths,
            total_distance=total_distance,
            max_distance=max_distance,
            fitness=0.0
        )
        
        solution.is_valid = self.is_solution_valid(solution)
        solution.fitness = self.evaluate_solution(solution)
        
        return solution
        
    def _optimize_path_order(self, targets: List[int]) -> List[int]:
        """使用最近邻算法优化路径顺序"""
        if not targets:
            return []
            
        if len(targets) == 1:
            return targets
            
        # 最近邻算法
        unvisited = set(targets)
        path = []
        current = 0  # 从起始点开始
        
        while unvisited:
            nearest = min(unvisited, key=lambda t: self.distance_matrix[current][t + 1])
            path.append(nearest)
            unvisited.remove(nearest)
            current = nearest + 1
            
        return path
        
    def _selection(self, population: List[List[int]], fitness_scores: List[float]) -> List[List[int]]:
        """选择操作"""
        # 锦标赛选择
        selected = []
        tournament_size = 3
        
        for _ in range(len(population)):
            # 随机选择参赛者
            tournament_indices = random.sample(range(len(population)), tournament_size)
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]
            
            # 选择最佳个体
            winner_idx = tournament_indices[tournament_fitness.index(min(tournament_fitness))]
            selected.append(copy.deepcopy(population[winner_idx]))
            
        return selected
        
    def _crossover(self, population: List[List[int]]) -> List[List[int]]:
        """交叉操作"""
        offspring = []
        
        for i in range(0, len(population), 2):
            parent1 = population[i]
            parent2 = population[i + 1] if i + 1 < len(population) else population[0]
            
            if random.random() < self.crossover_rate:
                child1, child2 = self._uniform_crossover(parent1, parent2)
                offspring.extend([child1, child2])
            else:
                offspring.extend([copy.deepcopy(parent1), copy.deepcopy(parent2)])
                
        return offspring
        
    def _uniform_crossover(self, parent1: List[int], parent2: List[int]) -> Tuple[List[int], List[int]]:
        """均匀交叉"""
        child1 = []
        child2 = []
        
        for i in range(len(parent1)):
            if random.random() < 0.5:
                child1.append(parent1[i])
                child2.append(parent2[i])
            else:
                child1.append(parent2[i])
                child2.append(parent1[i])
                
        return child1, child2
        
    def _mutation(self, population: List[List[int]]) -> List[List[int]]:
        """变异操作"""
        for individual in population:
            for i in range(len(individual)):
                if random.random() < self.mutation_rate:
                    # 随机改变目标分配的无人机
                    individual[i] = random.randint(0, len(self.drones) - 1)
                    
        return population
        
    def _update_population(
        self,
        old_population: List[List[int]],
        offspring: List[List[int]],
        fitness_scores: List[float]
    ) -> List[List[int]]:
        """更新种群"""
        # 精英保留策略
        combined_population = old_population + offspring
        combined_fitness = []
        
        # 计算所有个体的适应度
        for individual in combined_population:
            solution = self._decode_individual(individual)
            fitness = self.evaluate_solution(solution)
            combined_fitness.append(fitness)
            
        # 选择最佳个体
        sorted_indices = sorted(range(len(combined_fitness)), key=lambda i: combined_fitness[i])
        new_population = [combined_population[i] for i in sorted_indices[:self.population_size]]
        
        return new_population
        
    def _create_fallback_solution(self) -> PathSolution:
        """创建备用解决方案"""
        # 简单的轮询分配
        paths = [[] for _ in range(len(self.drones))]
        
        for i, target_idx in enumerate(range(len(self.targets))):
            drone_idx = i % len(self.drones)
            paths[drone_idx].append(target_idx)
            
        # 计算距离
        path_distances = []
        for path in paths:
            distance = self.calculate_path_distance(path, self.distance_matrix)
            path_distances.append(distance)
            
        total_distance = sum(path_distances)
        max_distance = max(path_distances) if path_distances else 0
        
        solution = PathSolution(
            paths=paths,
            total_distance=total_distance,
            max_distance=max_distance,
            fitness=total_distance
        )
        
        solution.is_valid = self.is_solution_valid(solution)
        
        return solution
