"""
数据模型定义
"""
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid

Base = declarative_base()


# Pydantic 模型 (API 数据传输)
class DroneStatusEnum(str, Enum):
    IDLE = "IDLE"
    FLYING = "FLYING"
    CHARGING = "CHARGING"
    MAINTENANCE = "MAINTENANCE"


class MissionStatusEnum(str, Enum):
    PLANNING = "PLANNING"
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class FlightPathStatusEnum(str, Enum):
    PLANNED = "PLANNED"
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class Coordinate(BaseModel):
    """坐标模型"""
    latitude: float = Field(..., ge=-90, le=90, description="纬度")
    longitude: float = Field(..., ge=-180, le=180, description="经度")
    altitude: float = Field(default=0.0, ge=0, description="海拔高度(米)")


class DroneSpec(BaseModel):
    """无人机规格模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., description="无人机名称")
    model: str = Field(..., description="无人机型号")
    max_speed: float = Field(..., gt=0, description="最大速度(m/s)")
    max_altitude: float = Field(..., gt=0, description="最大飞行高度(米)")
    battery_life: float = Field(..., gt=0, description="电池续航时间(秒)")
    payload_capacity: float = Field(..., ge=0, description="载荷容量(kg)")
    capabilities: List[str] = Field(default_factory=list, description="能力列表")


class DroneStatus(BaseModel):
    """无人机状态模型"""
    drone_id: str = Field(..., description="无人机ID")
    position: Coordinate = Field(..., description="当前位置")
    speed: float = Field(default=0.0, ge=0, description="当前速度(m/s)")
    heading: float = Field(default=0.0, ge=0, lt=360, description="航向角(度)")
    battery_level: float = Field(..., ge=0, le=1, description="电池电量(0-1)")
    status: DroneStatusEnum = Field(..., description="状态")
    last_update: datetime = Field(default_factory=datetime.utcnow)


class Target(BaseModel):
    """目标模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    batch_id: Optional[str] = Field(None, description="批次ID")
    type: str = Field(..., description="目标类型")
    position: Coordinate = Field(..., description="目标位置")
    confidence: float = Field(..., ge=0, le=1, description="置信度")
    description: Optional[str] = Field(None, description="目标描述")
    detected_at: datetime = Field(default_factory=datetime.utcnow)


class Waypoint(BaseModel):
    """航点模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    position: Coordinate = Field(..., description="航点位置")
    speed: float = Field(..., gt=0, description="飞行速度(m/s)")
    altitude: float = Field(..., ge=0, description="飞行高度(米)")
    action: str = Field(default="SURVEY", description="执行动作")
    duration: int = Field(default=0, ge=0, description="停留时间(秒)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class FlightPath(BaseModel):
    """飞行路径模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    drone_id: str = Field(..., description="无人机ID")
    waypoints: List[Waypoint] = Field(..., description="航点列表")
    total_distance: float = Field(default=0.0, ge=0, description="总距离(米)")
    estimated_duration: int = Field(default=0, ge=0, description="预计耗时(秒)")
    status: FlightPathStatusEnum = Field(default=FlightPathStatusEnum.PLANNED)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class Mission(BaseModel):
    """任务模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    target_ids: List[str] = Field(default_factory=list, description="目标ID列表")
    flight_paths: List[FlightPath] = Field(default_factory=list, description="飞行路径列表")
    status: MissionStatusEnum = Field(default=MissionStatusEnum.PLANNING)
    area_center: Coordinate = Field(..., description="任务区域中心")
    area_radius: float = Field(..., gt=0, description="任务区域半径(米)")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = Field(None)
    completed_at: Optional[datetime] = Field(None)
    parameters: Dict[str, Any] = Field(default_factory=dict, description="任务参数")


class PlanningRequest(BaseModel):
    """规划请求模型"""
    user_id: str = Field(..., description="用户ID")
    conversation_id: str = Field(..., description="对话ID")
    message: str = Field(..., description="用户消息")
    targets: List[Target] = Field(default_factory=list, description="目标列表")
    available_drones: List[DroneSpec] = Field(default_factory=list, description="可用无人机")
    area_center: Optional[Coordinate] = Field(None, description="区域中心")
    area_radius: Optional[float] = Field(None, gt=0, description="区域半径")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="约束条件")


class PlanningResponse(BaseModel):
    """规划响应模型"""
    conversation_id: str = Field(..., description="对话ID")
    response_text: str = Field(..., description="响应文本")
    suggested_mission: Optional[Mission] = Field(None, description="建议任务")
    reasoning_steps: List[str] = Field(default_factory=list, description="推理步骤")
    confidence_score: float = Field(..., ge=0, le=1, description="置信度")
    alternatives: List[str] = Field(default_factory=list, description="备选方案")


# SQLAlchemy 数据库模型
class DroneSpecDB(Base):
    """无人机规格数据库模型"""
    __tablename__ = "drone_specs"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    model = Column(String, nullable=False)
    max_speed = Column(Float, nullable=False)
    max_altitude = Column(Float, nullable=False)
    battery_life = Column(Float, nullable=False)
    payload_capacity = Column(Float, nullable=False)
    capabilities = Column(JSON, default=list)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class DroneStatusDB(Base):
    """无人机状态数据库模型"""
    __tablename__ = "drone_status"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    drone_id = Column(String, nullable=False, index=True)
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    altitude = Column(Float, default=0.0)
    speed = Column(Float, default=0.0)
    heading = Column(Float, default=0.0)
    battery_level = Column(Float, nullable=False)
    status = Column(String, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)


class TargetDB(Base):
    """目标数据库模型"""
    __tablename__ = "targets"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    batch_id = Column(String, index=True)
    type = Column(String, nullable=False)
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    altitude = Column(Float, default=0.0)
    confidence = Column(Float, nullable=False)
    description = Column(Text)
    detected_at = Column(DateTime, default=datetime.utcnow, index=True)


class MissionDB(Base):
    """任务数据库模型"""
    __tablename__ = "missions"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    description = Column(Text)
    target_ids = Column(JSON, default=list)
    flight_paths = Column(JSON, default=list)
    status = Column(String, nullable=False, default="PLANNING")
    area_center_lat = Column(Float, nullable=False)
    area_center_lon = Column(Float, nullable=False)
    area_center_alt = Column(Float, default=0.0)
    area_radius = Column(Float, nullable=False)
    parameters = Column(JSON, default=dict)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ConversationDB(Base):
    """对话数据库模型"""
    __tablename__ = "conversations"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, nullable=False, index=True)
    messages = Column(JSON, default=list)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
