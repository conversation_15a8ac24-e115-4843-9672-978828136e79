# FastAPI 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# WebSocket 支持
websockets==12.0
python-socketio==5.10.0

# 数据库相关
asyncpg==0.29.0
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# 向量数据库
qdrant-client==1.7.0
sentence-transformers==2.2.2

# AI 模型集成
openai==1.3.7
transformers==4.36.2
torch==2.1.1
numpy==1.24.3

# gRPC 和 Protocol Buffers
grpcio==1.59.3
grpcio-tools==1.59.3
protobuf==4.25.1

# 地理空间数据处理
geojson==3.1.0
shapely==2.0.2
geopandas==0.14.1

# 优化算法
scipy==1.11.4
scikit-learn==1.3.2
deap==1.4.1

# 对象存储
minio==7.2.0

# 缓存
redis==5.0.1
aioredis==2.0.1

# HTTP 客户端
httpx==0.25.2
aiohttp==3.9.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 时间序列数据
pandas==2.1.4
