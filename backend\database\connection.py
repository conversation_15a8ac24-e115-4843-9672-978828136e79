"""
数据库连接管理模块
"""
import asyncio
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
import redis.asyncio as redis
from qdrant_client import QdrantClient
from qdrant_client.http import models
from minio import Minio
from loguru import logger

from app.config import get_settings
from app.models import Base

settings = get_settings()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.pg_engine = None
        self.timescale_engine = None
        self.pg_session_factory = None
        self.timescale_session_factory = None
        self.redis_client = None
        self.qdrant_client = None
        self.minio_client = None
        
    async def initialize(self):
        """初始化所有数据库连接"""
        await self._init_postgresql()
        await self._init_timescaledb()
        await self._init_redis()
        await self._init_qdrant()
        await self._init_minio()

        # 初始化RAG服务
        from ai_engine.rag_service import rag_service
        await rag_service.initialize()

        logger.info("所有数据库连接初始化完成")
        
    async def _init_postgresql(self):
        """初始化PostgreSQL连接"""
        try:
            self.pg_engine = create_async_engine(
                settings.database_url,
                pool_size=settings.database_pool_size,
                max_overflow=settings.database_max_overflow,
                echo=settings.debug,
                poolclass=NullPool if settings.debug else None
            )
            
            self.pg_session_factory = async_sessionmaker(
                self.pg_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # 创建表
            async with self.pg_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
                
            logger.info("PostgreSQL 连接初始化成功")
        except Exception as e:
            logger.error(f"PostgreSQL 连接初始化失败: {e}")
            raise
            
    async def _init_timescaledb(self):
        """初始化TimescaleDB连接"""
        try:
            self.timescale_engine = create_async_engine(
                settings.timescale_url,
                pool_size=10,
                max_overflow=20,
                echo=settings.debug
            )
            
            self.timescale_session_factory = async_sessionmaker(
                self.timescale_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            logger.info("TimescaleDB 连接初始化成功")
        except Exception as e:
            logger.error(f"TimescaleDB 连接初始化失败: {e}")
            raise
            
    async def _init_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis 连接初始化成功")
        except Exception as e:
            logger.error(f"Redis 连接初始化失败: {e}")
            raise
            
    async def _init_qdrant(self):
        """初始化Qdrant向量数据库连接"""
        try:
            self.qdrant_client = QdrantClient(
                host=settings.qdrant_host,
                port=settings.qdrant_port,
                api_key=settings.qdrant_api_key
            )
            
            # 创建集合（如果不存在）
            collections = self.qdrant_client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            if settings.qdrant_collection_name not in collection_names:
                self.qdrant_client.create_collection(
                    collection_name=settings.qdrant_collection_name,
                    vectors_config=models.VectorParams(
                        size=384,  # all-MiniLM-L6-v2 embedding size
                        distance=models.Distance.COSINE
                    )
                )
                logger.info(f"创建Qdrant集合: {settings.qdrant_collection_name}")
                
            logger.info("Qdrant 连接初始化成功")
        except Exception as e:
            logger.error(f"Qdrant 连接初始化失败: {e}")
            raise
            
    async def _init_minio(self):
        """初始化MinIO对象存储连接"""
        try:
            self.minio_client = Minio(
                settings.minio_endpoint,
                access_key=settings.minio_access_key,
                secret_key=settings.minio_secret_key,
                secure=settings.minio_secure
            )
            
            # 创建存储桶（如果不存在）
            if not self.minio_client.bucket_exists(settings.minio_bucket_name):
                self.minio_client.make_bucket(settings.minio_bucket_name)
                logger.info(f"创建MinIO存储桶: {settings.minio_bucket_name}")
                
            logger.info("MinIO 连接初始化成功")
        except Exception as e:
            logger.error(f"MinIO 连接初始化失败: {e}")
            raise
            
    async def close(self):
        """关闭所有数据库连接"""
        if self.pg_engine:
            await self.pg_engine.dispose()
        if self.timescale_engine:
            await self.timescale_engine.dispose()
        if self.redis_client:
            await self.redis_client.close()
        if self.qdrant_client:
            self.qdrant_client.close()
        logger.info("所有数据库连接已关闭")
        
    @asynccontextmanager
    async def get_pg_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取PostgreSQL会话"""
        async with self.pg_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
                
    @asynccontextmanager
    async def get_timescale_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取TimescaleDB会话"""
        async with self.timescale_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()


# 全局数据库管理器实例
db_manager = DatabaseManager()


async def get_pg_session() -> AsyncGenerator[AsyncSession, None]:
    """依赖注入：获取PostgreSQL会话"""
    async with db_manager.get_pg_session() as session:
        yield session


async def get_timescale_session() -> AsyncGenerator[AsyncSession, None]:
    """依赖注入：获取TimescaleDB会话"""
    async with db_manager.get_timescale_session() as session:
        yield session


async def get_redis_client():
    """依赖注入：获取Redis客户端"""
    return db_manager.redis_client


async def get_qdrant_client():
    """依赖注入：获取Qdrant客户端"""
    return db_manager.qdrant_client


async def get_minio_client():
    """依赖注入：获取MinIO客户端"""
    return db_manager.minio_client
