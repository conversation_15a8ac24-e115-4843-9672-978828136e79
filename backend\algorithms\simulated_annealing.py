"""
模拟退火算法实现
"""
import random
import math
import copy
from typing import List, Dict, Any
from loguru import logger

from algorithms.base import PathOptimizer, PathSolution
from app.models import Target, DroneSpec


class SimulatedAnnealing(PathOptimizer):
    """模拟退火算法路径优化器"""
    
    def __init__(
        self,
        targets: List[Target],
        drones: List[DroneSpec],
        constraints: Dict[str, Any],
        initial_temperature: float = 1000.0,
        cooling_rate: float = 0.95,
        min_temperature: float = 1.0
    ):
        super().__init__(targets, drones, constraints)
        self.initial_temperature = initial_temperature
        self.cooling_rate = cooling_rate
        self.min_temperature = min_temperature
        self.distance_matrix = self.calculate_distance_matrix()
        
    def optimize(self, max_iterations: int = 1000) -> PathSolution:
        """使用模拟退火算法优化路径"""
        try:
            logger.info(f"开始模拟退火算法优化: 目标数={len(self.targets)}, 无人机数={len(self.drones)}")
            
            # 生成初始解
            current_solution = self._generate_initial_solution()
            best_solution = copy.deepcopy(current_solution)
            
            temperature = self.initial_temperature
            iteration = 0
            
            while temperature > self.min_temperature and iteration < max_iterations:
                # 生成邻域解
                neighbor_solution = self._generate_neighbor(current_solution)
                
                # 计算能量差
                delta_energy = neighbor_solution.fitness - current_solution.fitness
                
                # 接受准则
                if delta_energy < 0 or random.random() < math.exp(-delta_energy / temperature):
                    current_solution = neighbor_solution
                    
                    # 更新最佳解
                    if current_solution.fitness < best_solution.fitness:
                        best_solution = copy.deepcopy(current_solution)
                        
                # 降温
                temperature *= self.cooling_rate
                iteration += 1
                
                # 记录进度
                if iteration % 100 == 0:
                    logger.debug(f"模拟退火第 {iteration} 次迭代，温度: {temperature:.2f}, 最佳适应度: {best_solution.fitness:.2f}")
                    
            logger.info(f"模拟退火算法优化完成，最佳适应度: {best_solution.fitness:.2f}")
            return best_solution
            
        except Exception as e:
            logger.error(f"模拟退火算法优化失败: {e}")
            return self._create_fallback_solution()
            
    def _generate_initial_solution(self) -> PathSolution:
        """生成初始解"""
        # 使用最近邻算法生成初始解
        paths = [[] for _ in range(len(self.drones))]
        unassigned_targets = list(range(len(self.targets)))
        
        # 为每个无人机分配目标
        for drone_idx in range(len(self.drones)):
            if not unassigned_targets:
                break
                
            # 计算每个无人机应分配的目标数量
            remaining_drones = len(self.drones) - drone_idx
            targets_per_drone = len(unassigned_targets) // remaining_drones
            if drone_idx < len(unassigned_targets) % remaining_drones:
                targets_per_drone += 1
                
            # 使用最近邻算法为当前无人机选择目标
            drone_targets = self._nearest_neighbor_selection(
                unassigned_targets, targets_per_drone
            )
            
            paths[drone_idx] = drone_targets
            
            # 移除已分配的目标
            for target in drone_targets:
                unassigned_targets.remove(target)
                
        return self._evaluate_paths(paths)
        
    def _nearest_neighbor_selection(self, available_targets: List[int], count: int) -> List[int]:
        """使用最近邻算法选择目标"""
        if not available_targets or count <= 0:
            return []
            
        selected = []
        remaining = available_targets.copy()
        current_position = 0  # 从起始点开始
        
        for _ in range(min(count, len(remaining))):
            # 找到最近的目标
            nearest_target = min(
                remaining,
                key=lambda t: self.distance_matrix[current_position][t + 1]
            )
            
            selected.append(nearest_target)
            remaining.remove(nearest_target)
            current_position = nearest_target + 1
            
        return selected
        
    def _generate_neighbor(self, solution: PathSolution) -> PathSolution:
        """生成邻域解"""
        new_solution = copy.deepcopy(solution)
        
        # 随机选择邻域操作
        operation = random.choice([
            'swap_within_path',
            'move_between_paths',
            'reverse_path_segment',
            'relocate_target'
        ])
        
        if operation == 'swap_within_path':
            self._swap_within_path(new_solution)
        elif operation == 'move_between_paths':
            self._move_between_paths(new_solution)
        elif operation == 'reverse_path_segment':
            self._reverse_path_segment(new_solution)
        elif operation == 'relocate_target':
            self._relocate_target(new_solution)
            
        # 重新评估解决方案
        return self._evaluate_paths(new_solution.paths)
        
    def _swap_within_path(self, solution: PathSolution):
        """路径内交换"""
        non_empty_paths = [i for i, path in enumerate(solution.paths) if len(path) >= 2]
        
        if not non_empty_paths:
            return
            
        path_idx = random.choice(non_empty_paths)
        path = solution.paths[path_idx]
        
        # 随机选择两个位置交换
        i, j = random.sample(range(len(path)), 2)
        path[i], path[j] = path[j], path[i]
        
    def _move_between_paths(self, solution: PathSolution):
        """路径间移动"""
        non_empty_paths = [i for i, path in enumerate(solution.paths) if path]
        
        if len(non_empty_paths) < 2:
            return
            
        # 选择源路径和目标路径
        source_idx = random.choice(non_empty_paths)
        target_idx = random.choice([i for i in range(len(solution.paths)) if i != source_idx])
        
        source_path = solution.paths[source_idx]
        target_path = solution.paths[target_idx]
        
        # 移动一个目标
        if source_path:
            target_to_move = random.choice(source_path)
            source_path.remove(target_to_move)
            
            # 插入到目标路径的随机位置
            insert_position = random.randint(0, len(target_path))
            target_path.insert(insert_position, target_to_move)
            
    def _reverse_path_segment(self, solution: PathSolution):
        """反转路径片段"""
        non_empty_paths = [i for i, path in enumerate(solution.paths) if len(path) >= 2]
        
        if not non_empty_paths:
            return
            
        path_idx = random.choice(non_empty_paths)
        path = solution.paths[path_idx]
        
        # 选择片段
        start = random.randint(0, len(path) - 2)
        end = random.randint(start + 1, len(path) - 1)
        
        # 反转片段
        path[start:end + 1] = path[start:end + 1][::-1]
        
    def _relocate_target(self, solution: PathSolution):
        """重新定位目标"""
        non_empty_paths = [i for i, path in enumerate(solution.paths) if path]
        
        if not non_empty_paths:
            return
            
        # 选择源路径
        source_idx = random.choice(non_empty_paths)
        source_path = solution.paths[source_idx]
        
        # 选择要移动的目标
        target_to_move = random.choice(source_path)
        source_path.remove(target_to_move)
        
        # 选择新位置
        new_path_idx = random.randint(0, len(solution.paths) - 1)
        new_path = solution.paths[new_path_idx]
        
        insert_position = random.randint(0, len(new_path))
        new_path.insert(insert_position, target_to_move)
        
    def _evaluate_paths(self, paths: List[List[int]]) -> PathSolution:
        """评估路径质量"""
        path_distances = []
        
        for path in paths:
            distance = self.calculate_path_distance(path, self.distance_matrix)
            path_distances.append(distance)
            
        total_distance = sum(path_distances)
        max_distance = max(path_distances) if path_distances else 0
        
        solution = PathSolution(
            paths=paths,
            total_distance=total_distance,
            max_distance=max_distance,
            fitness=0.0
        )
        
        solution.is_valid = self.is_solution_valid(solution)
        solution.fitness = self.evaluate_solution(solution)
        
        return solution
        
    def _create_fallback_solution(self) -> PathSolution:
        """创建备用解决方案"""
        # 简单的轮询分配
        paths = [[] for _ in range(len(self.drones))]
        
        for i, target_idx in enumerate(range(len(self.targets))):
            drone_idx = i % len(self.drones)
            paths[drone_idx].append(target_idx)
            
        return self._evaluate_paths(paths)
