"""
Cesium 可视化服务
"""
import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import grpc
from concurrent import futures
from loguru import logger

from app.config import get_settings
from app.models import Mission, FlightPath, DroneStatus, Target, Coordinate

settings = get_settings()


class CesiumVisualizationService:
    """Cesium 可视化服务"""
    
    def __init__(self):
        self.active_sessions = {}  # session_id -> session_data
        self.cesium_token = settings.cesium_ion_access_token
        
    async def initialize_session(
        self,
        session_id: str,
        view_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """初始化可视化会话"""
        try:
            session_data = {
                "session_id": session_id,
                "created_at": datetime.utcnow(),
                "view_options": view_options or {},
                "entities": {
                    "drones": {},
                    "targets": {},
                    "flight_paths": {},
                    "waypoints": {}
                },
                "camera_position": {
                    "longitude": 116.4074,
                    "latitude": 39.9042,
                    "height": 10000
                }
            }
            
            self.active_sessions[session_id] = session_data
            
            # 生成Cesium配置
            cesium_config = self._generate_cesium_config(session_data)
            
            logger.info(f"初始化Cesium会话: {session_id}")
            return {
                "session_id": session_id,
                "cesium_config": cesium_config,
                "status": "initialized"
            }
            
        except Exception as e:
            logger.error(f"初始化Cesium会话失败: {e}")
            raise
            
    def _generate_cesium_config(self, session_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成Cesium配置"""
        config = {
            "ion": {
                "defaultAccessToken": self.cesium_token
            },
            "viewer": {
                "terrainProvider": "Cesium.createWorldTerrain()",
                "imageryProvider": "new Cesium.IonImageryProvider({ assetId: 3 })",
                "scene3DOnly": False,
                "homeButton": True,
                "sceneModePicker": True,
                "navigationHelpButton": False,
                "animation": True,
                "timeline": True,
                "fullscreenButton": True,
                "vrButton": False
            },
            "camera": {
                "destination": {
                    "longitude": session_data["camera_position"]["longitude"],
                    "latitude": session_data["camera_position"]["latitude"],
                    "height": session_data["camera_position"]["height"]
                }
            },
            "entities": [],
            "dataSources": []
        }
        
        return config
        
    async def add_mission_visualization(
        self,
        session_id: str,
        mission: Mission
    ) -> Dict[str, Any]:
        """添加任务可视化"""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"会话不存在: {session_id}")
                
            session_data = self.active_sessions[session_id]
            
            # 添加任务区域
            mission_area = self._create_mission_area_entity(mission)
            session_data["entities"]["mission_area"] = mission_area
            
            # 添加飞行路径
            for flight_path in mission.flight_paths:
                path_entity = self._create_flight_path_entity(flight_path)
                session_data["entities"]["flight_paths"][flight_path.id] = path_entity
                
                # 添加航点
                for waypoint in flight_path.waypoints:
                    waypoint_entity = self._create_waypoint_entity(waypoint, flight_path.drone_id)
                    session_data["entities"]["waypoints"][waypoint.id] = waypoint_entity
                    
            # 更新相机位置到任务中心
            session_data["camera_position"] = {
                "longitude": mission.area_center.longitude,
                "latitude": mission.area_center.latitude,
                "height": mission.area_radius * 2
            }
            
            logger.info(f"添加任务可视化: {mission.id}")
            return {
                "session_id": session_id,
                "mission_id": mission.id,
                "entities_added": len(session_data["entities"]["flight_paths"]),
                "status": "mission_added"
            }
            
        except Exception as e:
            logger.error(f"添加任务可视化失败: {e}")
            raise
            
    def _create_mission_area_entity(self, mission: Mission) -> Dict[str, Any]:
        """创建任务区域实体"""
        return {
            "id": f"mission_area_{mission.id}",
            "name": f"任务区域 - {mission.name}",
            "position": {
                "longitude": mission.area_center.longitude,
                "latitude": mission.area_center.latitude,
                "height": mission.area_center.altitude
            },
            "ellipse": {
                "semiMajorAxis": mission.area_radius,
                "semiMinorAxis": mission.area_radius,
                "material": {
                    "color": "rgba(255, 255, 0, 0.3)"
                },
                "outline": True,
                "outlineColor": "yellow",
                "height": 0
            },
            "label": {
                "text": mission.name,
                "font": "12pt sans-serif",
                "fillColor": "white",
                "outlineColor": "black",
                "outlineWidth": 2,
                "style": "FILL_AND_OUTLINE",
                "pixelOffset": {"x": 0, "y": -40}
            }
        }
        
    def _create_flight_path_entity(self, flight_path: FlightPath) -> Dict[str, Any]:
        """创建飞行路径实体"""
        # 构建路径坐标
        positions = []
        for waypoint in flight_path.waypoints:
            positions.extend([
                waypoint.position.longitude,
                waypoint.position.latitude,
                waypoint.position.altitude
            ])
            
        return {
            "id": f"flight_path_{flight_path.id}",
            "name": f"飞行路径 - {flight_path.drone_id}",
            "polyline": {
                "positions": positions,
                "width": 3,
                "material": {
                    "color": self._get_drone_color(flight_path.drone_id)
                },
                "clampToGround": False,
                "followSurface": False
            },
            "metadata": {
                "drone_id": flight_path.drone_id,
                "total_distance": flight_path.total_distance,
                "estimated_duration": flight_path.estimated_duration,
                "status": flight_path.status.value
            }
        }
        
    def _create_waypoint_entity(self, waypoint, drone_id: str) -> Dict[str, Any]:
        """创建航点实体"""
        return {
            "id": f"waypoint_{waypoint.id}",
            "name": f"航点 - {waypoint.action}",
            "position": {
                "longitude": waypoint.position.longitude,
                "latitude": waypoint.position.latitude,
                "height": waypoint.position.altitude
            },
            "point": {
                "pixelSize": 8,
                "color": self._get_waypoint_color(waypoint.action),
                "outlineColor": "white",
                "outlineWidth": 2,
                "heightReference": "RELATIVE_TO_GROUND"
            },
            "label": {
                "text": waypoint.action,
                "font": "10pt sans-serif",
                "fillColor": "white",
                "outlineColor": "black",
                "outlineWidth": 1,
                "style": "FILL_AND_OUTLINE",
                "pixelOffset": {"x": 0, "y": -20},
                "show": False  # 默认隐藏，鼠标悬停时显示
            },
            "metadata": {
                "drone_id": drone_id,
                "action": waypoint.action,
                "speed": waypoint.speed,
                "duration": waypoint.duration
            }
        }
        
    def _get_drone_color(self, drone_id: str) -> str:
        """获取无人机颜色"""
        colors = [
            "rgba(255, 0, 0, 0.8)",    # 红色
            "rgba(0, 255, 0, 0.8)",    # 绿色
            "rgba(0, 0, 255, 0.8)",    # 蓝色
            "rgba(255, 255, 0, 0.8)",  # 黄色
            "rgba(255, 0, 255, 0.8)",  # 紫色
            "rgba(0, 255, 255, 0.8)",  # 青色
        ]
        
        # 根据drone_id哈希选择颜色
        color_index = hash(drone_id) % len(colors)
        return colors[color_index]
        
    def _get_waypoint_color(self, action: str) -> str:
        """获取航点颜色"""
        color_map = {
            "TAKEOFF": "green",
            "LANDING": "red",
            "SURVEY": "blue",
            "HOVER": "yellow",
            "PHOTO": "purple"
        }
        return color_map.get(action, "white")
        
    async def update_drone_position(
        self,
        session_id: str,
        drone_status: DroneStatus
    ) -> Dict[str, Any]:
        """更新无人机位置"""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"会话不存在: {session_id}")
                
            session_data = self.active_sessions[session_id]
            
            # 创建或更新无人机实体
            drone_entity = self._create_drone_entity(drone_status)
            session_data["entities"]["drones"][drone_status.drone_id] = drone_entity
            
            logger.debug(f"更新无人机位置: {drone_status.drone_id}")
            return {
                "session_id": session_id,
                "drone_id": drone_status.drone_id,
                "position": {
                    "longitude": drone_status.position.longitude,
                    "latitude": drone_status.position.latitude,
                    "altitude": drone_status.position.altitude
                },
                "status": "position_updated"
            }
            
        except Exception as e:
            logger.error(f"更新无人机位置失败: {e}")
            raise
            
    def _create_drone_entity(self, drone_status: DroneStatus) -> Dict[str, Any]:
        """创建无人机实体"""
        return {
            "id": f"drone_{drone_status.drone_id}",
            "name": f"无人机 - {drone_status.drone_id}",
            "position": {
                "longitude": drone_status.position.longitude,
                "latitude": drone_status.position.latitude,
                "height": drone_status.position.altitude
            },
            "model": {
                "uri": "/models/drone.glb",  # 3D模型文件
                "scale": 1.0,
                "minimumPixelSize": 64,
                "maximumScale": 20000
            },
            "orientation": {
                "heading": drone_status.heading,
                "pitch": 0,
                "roll": 0
            },
            "label": {
                "text": f"{drone_status.drone_id}\n电量: {drone_status.battery_level*100:.0f}%",
                "font": "12pt sans-serif",
                "fillColor": "white",
                "outlineColor": "black",
                "outlineWidth": 2,
                "style": "FILL_AND_OUTLINE",
                "pixelOffset": {"x": 0, "y": -60}
            },
            "metadata": {
                "drone_id": drone_status.drone_id,
                "status": drone_status.status.value,
                "battery_level": drone_status.battery_level,
                "speed": drone_status.speed,
                "last_update": drone_status.last_update.isoformat()
            }
        }
        
    async def add_targets_visualization(
        self,
        session_id: str,
        targets: List[Target]
    ) -> Dict[str, Any]:
        """添加目标可视化"""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"会话不存在: {session_id}")
                
            session_data = self.active_sessions[session_id]
            
            for target in targets:
                target_entity = self._create_target_entity(target)
                session_data["entities"]["targets"][target.id] = target_entity
                
            logger.info(f"添加目标可视化: {len(targets)} 个目标")
            return {
                "session_id": session_id,
                "targets_added": len(targets),
                "status": "targets_added"
            }
            
        except Exception as e:
            logger.error(f"添加目标可视化失败: {e}")
            raise
            
    def _create_target_entity(self, target: Target) -> Dict[str, Any]:
        """创建目标实体"""
        return {
            "id": f"target_{target.id}",
            "name": f"目标 - {target.type}",
            "position": {
                "longitude": target.position.longitude,
                "latitude": target.position.latitude,
                "height": target.position.altitude
            },
            "billboard": {
                "image": f"/icons/{target.type.lower()}.png",
                "scale": 0.5,
                "verticalOrigin": "BOTTOM",
                "heightReference": "CLAMP_TO_GROUND"
            },
            "label": {
                "text": f"{target.type}\n置信度: {target.confidence*100:.0f}%",
                "font": "10pt sans-serif",
                "fillColor": "yellow",
                "outlineColor": "black",
                "outlineWidth": 1,
                "style": "FILL_AND_OUTLINE",
                "pixelOffset": {"x": 0, "y": -40},
                "show": False  # 默认隐藏
            },
            "metadata": {
                "target_id": target.id,
                "type": target.type,
                "confidence": target.confidence,
                "detected_at": target.detected_at.isoformat(),
                "batch_id": target.batch_id
            }
        }
        
    async def get_session_data(self, session_id: str) -> Dict[str, Any]:
        """获取会话数据"""
        try:
            if session_id not in self.active_sessions:
                raise ValueError(f"会话不存在: {session_id}")
                
            session_data = self.active_sessions[session_id]
            
            # 构建Cesium实体数据
            entities = []
            for entity_type, entity_dict in session_data["entities"].items():
                if isinstance(entity_dict, dict):
                    entities.extend(entity_dict.values())
                else:
                    entities.append(entity_dict)
                    
            return {
                "session_id": session_id,
                "cesium_config": self._generate_cesium_config(session_data),
                "entities": entities,
                "camera_position": session_data["camera_position"]
            }
            
        except Exception as e:
            logger.error(f"获取会话数据失败: {e}")
            raise
            
    async def remove_session(self, session_id: str):
        """移除会话"""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                logger.info(f"移除Cesium会话: {session_id}")
                
        except Exception as e:
            logger.error(f"移除会话失败: {e}")
            
    async def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计"""
        total_sessions = len(self.active_sessions)
        total_entities = 0
        
        for session_data in self.active_sessions.values():
            for entity_dict in session_data["entities"].values():
                if isinstance(entity_dict, dict):
                    total_entities += len(entity_dict)
                else:
                    total_entities += 1
                    
        return {
            "total_sessions": total_sessions,
            "total_entities": total_entities,
            "active_sessions": list(self.active_sessions.keys())
        }


# 全局Cesium服务实例
cesium_service = CesiumVisualizationService()


async def get_cesium_service() -> CesiumVisualizationService:
    """依赖注入：获取Cesium服务"""
    return cesium_service
