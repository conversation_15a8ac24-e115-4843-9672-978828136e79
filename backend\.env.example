# 应用配置
APP_NAME="Mission Planning System"
APP_VERSION="0.1.0"
DEBUG=true
LOG_LEVEL="INFO"

# 服务器配置
HOST="0.0.0.0"
PORT=8000
WORKERS=1

# 数据库配置
DATABASE_URL="postgresql+asyncpg://postgres:password@localhost:5432/missionplanning"
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# 时间序列数据库 (TimescaleDB)
TIMESCALE_URL="postgresql+asyncpg://postgres:password@localhost:5432/timescale"

# Redis 配置
REDIS_URL="redis://localhost:6379/0"
REDIS_CACHE_TTL=3600

# Qdrant 向量数据库
QDRANT_HOST="localhost"
QDRANT_PORT=6333
QDRANT_API_KEY=""
QDRANT_COLLECTION_NAME="drone_knowledge"

# MinIO 对象存储
MINIO_ENDPOINT="localhost:9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_BUCKET_NAME="mission-data"
MINIO_SECURE=false

# AI 模型配置
QWEN_API_KEY=""
QWEN_API_BASE="https://api.qwen.com/v1"
QWEN_MODEL="qwen-turbo"
EMBEDDING_MODEL="sentence-transformers/all-MiniLM-L6-v2"

# Cesium 配置
CESIUM_ION_ACCESS_TOKEN=""
CESIUM_TERRAIN_PROVIDER=""

# gRPC 服务配置
GRPC_HOST="localhost"
GRPC_PORT=50051
GRPC_MAX_WORKERS=10

# WebSocket 配置
WEBSOCKET_PING_INTERVAL=20
WEBSOCKET_PING_TIMEOUT=10

# 安全配置
SECRET_KEY="your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# CORS 配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# 外部 API 配置
EXTERNAL_TARGET_API_URL=""
EXTERNAL_TARGET_API_KEY=""

# 监控配置
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# 算法配置
GENETIC_ALGORITHM_POPULATION_SIZE=100
GENETIC_ALGORITHM_GENERATIONS=50
GENETIC_ALGORITHM_MUTATION_RATE=0.1
GENETIC_ALGORITHM_CROSSOVER_RATE=0.8

# 无人机配置
MAX_DRONES_PER_MISSION=10
DEFAULT_DRONE_SPEED=15.0  # m/s
DEFAULT_DRONE_ALTITUDE=100.0  # meters
DEFAULT_BATTERY_LIFE=3600  # seconds
