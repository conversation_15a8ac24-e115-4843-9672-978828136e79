"""
FastAPI 主应用程序
"""
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
import uvicorn

from app.config import get_settings
from app.routers import missions, drones, targets, planning, websocket, knowledge, visualization
from database.connection import db_manager

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("启动飞行任务规划系统...")
    await db_manager.initialize()
    logger.info("系统启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("关闭系统...")
    await db_manager.close()
    logger.info("系统已关闭")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="综合性飞行任务规划系统 - 基于AI的多无人机协同侦察路线规划",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "内部服务器错误"}
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": settings.app_version,
        "timestamp": "2025-08-08T09:14:00Z"
    }


@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "飞行任务规划系统 API",
        "version": settings.app_version,
        "docs": "/docs" if settings.debug else "文档已禁用"
    }


# 注册路由
app.include_router(missions.router, prefix="/api/v1/missions", tags=["missions"])
app.include_router(drones.router, prefix="/api/v1/drones", tags=["drones"])
app.include_router(targets.router, prefix="/api/v1/targets", tags=["targets"])
app.include_router(planning.router, prefix="/api/v1/planning", tags=["planning"])
app.include_router(knowledge.router, prefix="/api/v1/knowledge", tags=["knowledge"])
app.include_router(visualization.router, prefix="/api/v1/visualization", tags=["visualization"])
app.include_router(websocket.router, prefix="/ws", tags=["websocket"])


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        workers=settings.workers if not settings.debug else 1,
        log_level=settings.log_level.lower(),
    )
