# 飞行任务规划系统 (Mission Planning System)

## 系统概述

一套综合性飞行任务规划系统，采用Qwen3-30B模型作为AI智能体，实现多无人机侦察路线规划，支持基于聊天交互的对话式任务规划与配置。

## 核心特性

- 🤖 **AI智能体**: 基于Qwen3-30B模型的对话式任务规划
- 🌍 **3D可视化**: 通过MCP协议和gRPC集成Cesium实现3D/2D航线可视化
- 🔍 **RAG知识库**: 基于Qdrant向量数据库的无人机资源知识检索
- 🛰️ **多机协同**: 遗传算法/蚁群算法优化的多无人机路径规划
- ⚡ **实时通信**: WebSocket双通道实时场景更新
- 📡 **外部集成**: RESTful API接收目标检测数据

## 技术架构

### 后端服务 (Python)
- **FastAPI + Flask**: 主服务框架
- **WebSocket**: 实时双向通信
- **gRPC**: Cesium可视化微服务
- **Qdrant**: 向量数据库
- **PostgreSQL + TimescaleDB**: 元数据存储
- **MinIO S3**: 轨迹数据存储

### 前端应用 (Vue 3)
- **Vue 3 + TypeScript**: 前端框架
- **Cesium + Resium**: 3D可视化引擎
- **RxJS + WebSocket**: 实时数据流
- **响应式设计**: 自适应渲染

### AI模块
- **Qwen3-30B**: 任务规划引擎
- **RAG检索**: 语义知识查询
- **优化算法**: 遗传/蚁群/模拟退火算法

## 项目结构

```
missionplansys/
├── backend/                 # 后端服务
│   ├── app/                # FastAPI应用
│   ├── ai_engine/          # AI智能体模块
│   ├── visualization/      # Cesium可视化服务
│   ├── algorithms/         # 航线规划算法
│   └── database/           # 数据库模块
├── frontend/               # Vue前端应用
├── shared/                 # 共享协议和类型定义
├── docs/                   # 文档
├── tests/                  # 测试
└── deployment/             # 部署配置
```

## 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 安装依赖

```bash
# 后端依赖
cd backend
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
```

### 启动服务

```bash
# 启动后端服务
cd backend
uvicorn app.main:app --reload

# 启动前端应用
cd frontend
npm run dev
```

## 开发指南

详细的开发文档请参考 [docs/](./docs/) 目录。

## 许可证

MIT License
