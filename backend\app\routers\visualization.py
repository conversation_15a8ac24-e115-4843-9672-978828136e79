"""
可视化路由
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from loguru import logger

from app.models import Mission, DroneStatus, Target
from visualization.cesium_service import get_cesium_service, CesiumVisualizationService

router = APIRouter()


class VisualizationRequest(BaseModel):
    """可视化请求模型"""
    session_id: str
    view_options: Optional[Dict[str, Any]] = None


class DronePositionUpdate(BaseModel):
    """无人机位置更新模型"""
    session_id: str
    drone_status: DroneStatus


@router.post("/sessions")
async def create_visualization_session(
    request: VisualizationRequest,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """创建可视化会话"""
    try:
        result = await cesium_service.initialize_session(
            session_id=request.session_id,
            view_options=request.view_options
        )
        
        logger.info(f"创建可视化会话: {request.session_id}")
        return result
        
    except Exception as e:
        logger.error(f"创建可视化会话失败: {e}")
        raise HTTPException(status_code=500, detail="创建可视化会话失败")


@router.get("/sessions/{session_id}")
async def get_session_data(
    session_id: str,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """获取会话数据"""
    try:
        session_data = await cesium_service.get_session_data(session_id)
        
        return session_data
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取会话数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取会话数据失败")


@router.delete("/sessions/{session_id}")
async def remove_session(
    session_id: str,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """移除会话"""
    try:
        await cesium_service.remove_session(session_id)
        
        return {"message": "会话移除成功"}
        
    except Exception as e:
        logger.error(f"移除会话失败: {e}")
        raise HTTPException(status_code=500, detail="移除会话失败")


@router.post("/sessions/{session_id}/mission")
async def add_mission_visualization(
    session_id: str,
    mission: Mission,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """添加任务可视化"""
    try:
        result = await cesium_service.add_mission_visualization(session_id, mission)
        
        logger.info(f"添加任务可视化: {mission.id}")
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"添加任务可视化失败: {e}")
        raise HTTPException(status_code=500, detail="添加任务可视化失败")


@router.post("/sessions/{session_id}/targets")
async def add_targets_visualization(
    session_id: str,
    targets: List[Target],
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """添加目标可视化"""
    try:
        result = await cesium_service.add_targets_visualization(session_id, targets)
        
        logger.info(f"添加目标可视化: {len(targets)} 个目标")
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"添加目标可视化失败: {e}")
        raise HTTPException(status_code=500, detail="添加目标可视化失败")


@router.put("/sessions/{session_id}/drone-position")
async def update_drone_position(
    session_id: str,
    drone_status: DroneStatus,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """更新无人机位置"""
    try:
        result = await cesium_service.update_drone_position(session_id, drone_status)
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"更新无人机位置失败: {e}")
        raise HTTPException(status_code=500, detail="更新无人机位置失败")


@router.get("/sessions/{session_id}/config")
async def get_cesium_config(
    session_id: str,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """获取Cesium配置"""
    try:
        session_data = await cesium_service.get_session_data(session_id)
        
        return {
            "cesium_config": session_data["cesium_config"],
            "camera_position": session_data["camera_position"]
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取Cesium配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取Cesium配置失败")


@router.get("/sessions/{session_id}/entities")
async def get_entities(
    session_id: str,
    entity_type: Optional[str] = Query(None, description="实体类型过滤"),
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """获取实体数据"""
    try:
        session_data = await cesium_service.get_session_data(session_id)
        entities = session_data["entities"]
        
        if entity_type:
            # 过滤特定类型的实体
            filtered_entities = [
                entity for entity in entities
                if entity.get("id", "").startswith(entity_type)
            ]
            entities = filtered_entities
            
        return {
            "session_id": session_id,
            "entity_type": entity_type,
            "entities": entities,
            "count": len(entities)
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取实体数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取实体数据失败")


@router.get("/stats")
async def get_visualization_stats(
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """获取可视化统计"""
    try:
        stats = await cesium_service.get_session_stats()
        
        return stats
        
    except Exception as e:
        logger.error(f"获取可视化统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取可视化统计失败")


@router.get("/entity-types")
async def get_entity_types():
    """获取实体类型列表"""
    return {
        "entity_types": [
            {
                "type": "drone",
                "name": "无人机",
                "description": "实时无人机位置和状态",
                "icon": "drone.png"
            },
            {
                "type": "target",
                "name": "目标",
                "description": "检测到的目标位置",
                "icon": "target.png"
            },
            {
                "type": "flight_path",
                "name": "飞行路径",
                "description": "规划的飞行航线",
                "icon": "path.png"
            },
            {
                "type": "waypoint",
                "name": "航点",
                "description": "飞行路径上的关键点",
                "icon": "waypoint.png"
            },
            {
                "type": "mission_area",
                "name": "任务区域",
                "description": "任务覆盖区域",
                "icon": "area.png"
            }
        ]
    }


@router.post("/sessions/{session_id}/camera")
async def update_camera_position(
    session_id: str,
    longitude: float,
    latitude: float,
    height: float,
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """更新相机位置"""
    try:
        if session_id not in cesium_service.active_sessions:
            raise HTTPException(status_code=404, detail="会话不存在")
            
        session_data = cesium_service.active_sessions[session_id]
        session_data["camera_position"] = {
            "longitude": longitude,
            "latitude": latitude,
            "height": height
        }
        
        return {
            "session_id": session_id,
            "camera_position": session_data["camera_position"],
            "status": "camera_updated"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新相机位置失败: {e}")
        raise HTTPException(status_code=500, detail="更新相机位置失败")


@router.get("/sessions/{session_id}/export")
async def export_session_data(
    session_id: str,
    format: str = Query("json", description="导出格式: json, kml, czml"),
    cesium_service: CesiumVisualizationService = Depends(get_cesium_service)
):
    """导出会话数据"""
    try:
        session_data = await cesium_service.get_session_data(session_id)
        
        if format == "json":
            return session_data
        elif format == "kml":
            # TODO: 实现KML导出
            raise HTTPException(status_code=501, detail="KML导出功能尚未实现")
        elif format == "czml":
            # TODO: 实现CZML导出
            raise HTTPException(status_code=501, detail="CZML导出功能尚未实现")
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
            
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出会话数据失败: {e}")
        raise HTTPException(status_code=500, detail="导出会话数据失败")
