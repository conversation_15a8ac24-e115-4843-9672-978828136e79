"""
无人机管理路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from loguru import logger

from app.models import DroneSpec, DroneStatus, DroneSpecDB, DroneStatusDB, DroneStatusEnum
from database.connection import get_pg_session

router = APIRouter()


@router.post("/specs", response_model=DroneSpec)
async def register_drone(
    drone_spec: DroneSpec,
    db: AsyncSession = Depends(get_pg_session)
):
    """注册无人机规格"""
    try:
        # 转换为数据库模型
        drone_spec_db = DroneSpecDB(
            id=drone_spec.id,
            name=drone_spec.name,
            model=drone_spec.model,
            max_speed=drone_spec.max_speed,
            max_altitude=drone_spec.max_altitude,
            battery_life=drone_spec.battery_life,
            payload_capacity=drone_spec.payload_capacity,
            capabilities=drone_spec.capabilities
        )
        
        db.add(drone_spec_db)
        await db.commit()
        await db.refresh(drone_spec_db)
        
        logger.info(f"注册无人机成功: {drone_spec.id}")
        return drone_spec
        
    except Exception as e:
        logger.error(f"注册无人机失败: {e}")
        raise HTTPException(status_code=500, detail="注册无人机失败")


@router.get("/specs", response_model=List[DroneSpec])
async def list_drone_specs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_pg_session)
):
    """获取无人机规格列表"""
    try:
        # 分页查询
        offset = (page - 1) * page_size
        query = select(DroneSpecDB).offset(offset).limit(page_size)
        
        result = await db.execute(query)
        drone_specs_db = result.scalars().all()
        
        # 转换为Pydantic模型
        drone_specs = []
        for spec_db in drone_specs_db:
            spec = DroneSpec(
                id=spec_db.id,
                name=spec_db.name,
                model=spec_db.model,
                max_speed=spec_db.max_speed,
                max_altitude=spec_db.max_altitude,
                battery_life=spec_db.battery_life,
                payload_capacity=spec_db.payload_capacity,
                capabilities=spec_db.capabilities or []
            )
            drone_specs.append(spec)
            
        return drone_specs
        
    except Exception as e:
        logger.error(f"获取无人机规格列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取无人机规格列表失败")


@router.get("/specs/{drone_id}", response_model=DroneSpec)
async def get_drone_spec(
    drone_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """获取无人机规格详情"""
    try:
        query = select(DroneSpecDB).where(DroneSpecDB.id == drone_id)
        result = await db.execute(query)
        spec_db = result.scalar_one_or_none()
        
        if not spec_db:
            raise HTTPException(status_code=404, detail="无人机不存在")
            
        spec = DroneSpec(
            id=spec_db.id,
            name=spec_db.name,
            model=spec_db.model,
            max_speed=spec_db.max_speed,
            max_altitude=spec_db.max_altitude,
            battery_life=spec_db.battery_life,
            payload_capacity=spec_db.payload_capacity,
            capabilities=spec_db.capabilities or []
        )
        
        return spec
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取无人机规格详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取无人机规格详情失败")


@router.post("/status", response_model=DroneStatus)
async def update_drone_status(
    drone_status: DroneStatus,
    db: AsyncSession = Depends(get_pg_session)
):
    """更新无人机状态"""
    try:
        # 转换为数据库模型
        status_db = DroneStatusDB(
            drone_id=drone_status.drone_id,
            latitude=drone_status.position.latitude,
            longitude=drone_status.position.longitude,
            altitude=drone_status.position.altitude,
            speed=drone_status.speed,
            heading=drone_status.heading,
            battery_level=drone_status.battery_level,
            status=drone_status.status.value,
            timestamp=drone_status.last_update
        )
        
        db.add(status_db)
        await db.commit()
        await db.refresh(status_db)
        
        logger.info(f"更新无人机状态成功: {drone_status.drone_id}")
        return drone_status
        
    except Exception as e:
        logger.error(f"更新无人机状态失败: {e}")
        raise HTTPException(status_code=500, detail="更新无人机状态失败")


@router.get("/status/{drone_id}", response_model=DroneStatus)
async def get_drone_status(
    drone_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """获取无人机最新状态"""
    try:
        # 获取最新状态记录
        query = (
            select(DroneStatusDB)
            .where(DroneStatusDB.drone_id == drone_id)
            .order_by(DroneStatusDB.timestamp.desc())
            .limit(1)
        )
        
        result = await db.execute(query)
        status_db = result.scalar_one_or_none()
        
        if not status_db:
            raise HTTPException(status_code=404, detail="无人机状态不存在")
            
        # 转换为Pydantic模型
        status = DroneStatus(
            drone_id=status_db.drone_id,
            position={
                "latitude": status_db.latitude,
                "longitude": status_db.longitude,
                "altitude": status_db.altitude
            },
            speed=status_db.speed,
            heading=status_db.heading,
            battery_level=status_db.battery_level,
            status=DroneStatusEnum(status_db.status),
            last_update=status_db.timestamp
        )
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取无人机状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取无人机状态失败")


@router.get("/status", response_model=List[DroneStatus])
async def list_drone_status(
    status_filter: Optional[DroneStatusEnum] = Query(None, description="状态过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_pg_session)
):
    """获取无人机状态列表（每个无人机的最新状态）"""
    try:
        # 子查询：获取每个无人机的最新时间戳
        subquery = (
            select(
                DroneStatusDB.drone_id,
                select(DroneStatusDB.timestamp)
                .where(DroneStatusDB.drone_id == DroneStatusDB.drone_id)
                .order_by(DroneStatusDB.timestamp.desc())
                .limit(1)
                .scalar_subquery()
                .label("max_timestamp")
            )
            .distinct(DroneStatusDB.drone_id)
        ).subquery()
        
        # 主查询：获取最新状态记录
        query = (
            select(DroneStatusDB)
            .join(
                subquery,
                (DroneStatusDB.drone_id == subquery.c.drone_id) &
                (DroneStatusDB.timestamp == subquery.c.max_timestamp)
            )
        )
        
        if status_filter:
            query = query.where(DroneStatusDB.status == status_filter.value)
            
        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        result = await db.execute(query)
        status_list_db = result.scalars().all()
        
        # 转换为Pydantic模型
        status_list = []
        for status_db in status_list_db:
            status = DroneStatus(
                drone_id=status_db.drone_id,
                position={
                    "latitude": status_db.latitude,
                    "longitude": status_db.longitude,
                    "altitude": status_db.altitude
                },
                speed=status_db.speed,
                heading=status_db.heading,
                battery_level=status_db.battery_level,
                status=DroneStatusEnum(status_db.status),
                last_update=status_db.timestamp
            )
            status_list.append(status)
            
        return status_list
        
    except Exception as e:
        logger.error(f"获取无人机状态列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取无人机状态列表失败")


@router.delete("/specs/{drone_id}")
async def delete_drone_spec(
    drone_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """删除无人机规格"""
    try:
        # 检查无人机是否存在
        query = select(DroneSpecDB).where(DroneSpecDB.id == drone_id)
        result = await db.execute(query)
        existing_drone = result.scalar_one_or_none()
        
        if not existing_drone:
            raise HTTPException(status_code=404, detail="无人机不存在")
            
        # 删除无人机规格
        delete_query = delete(DroneSpecDB).where(DroneSpecDB.id == drone_id)
        await db.execute(delete_query)
        await db.commit()
        
        logger.info(f"删除无人机规格成功: {drone_id}")
        return {"message": "无人机规格删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除无人机规格失败: {e}")
        raise HTTPException(status_code=500, detail="删除无人机规格失败")
