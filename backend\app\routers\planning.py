"""
AI规划路由
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.models import PlanningRequest, PlanningResponse, Target, DroneSpec
from database.connection import get_pg_session
from ai_engine.qwen_client import get_qwen_client, QwenClient
from ai_engine.conversation_manager import get_conversation_manager, ConversationManager
from ai_engine.planning_service import PlanningService
from algorithms.optimizer_manager import get_optimizer_manager, OptimizerManager

router = APIRouter()


@router.post("/chat", response_model=PlanningResponse)
async def chat_planning(
    request: PlanningRequest,
    db: AsyncSession = Depends(get_pg_session),
    qwen_client: QwenClient = Depends(get_qwen_client),
    conversation_manager: ConversationManager = Depends(get_conversation_manager)
):
    """对话式任务规划"""
    try:
        # 创建规划服务
        planning_service = PlanningService(qwen_client, conversation_manager)

        # 处理规划请求
        response = await planning_service.process_planning_request(request, db)

        logger.info(f"处理规划请求: {request.conversation_id}")
        return response

    except Exception as e:
        logger.error(f"处理规划请求失败: {e}")
        raise HTTPException(status_code=500, detail="处理规划请求失败")


@router.post("/generate-mission")
async def generate_mission(
    request: PlanningRequest,
    db: AsyncSession = Depends(get_pg_session),
    qwen_client: QwenClient = Depends(get_qwen_client),
    conversation_manager: ConversationManager = Depends(get_conversation_manager)
):
    """生成详细任务方案"""
    try:
        # 创建规划服务
        planning_service = PlanningService(qwen_client, conversation_manager)

        # 生成详细任务
        mission = await planning_service.generate_detailed_mission(request, db)

        if not mission:
            raise HTTPException(status_code=400, detail="无法生成任务方案")

        logger.info(f"生成任务方案: {request.conversation_id}")
        return {
            "mission": mission,
            "message": "任务方案生成成功",
            "flight_paths_count": len(mission.flight_paths),
            "total_waypoints": sum(len(fp.waypoints) for fp in mission.flight_paths)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成任务方案失败: {e}")
        raise HTTPException(status_code=500, detail="生成任务方案失败")


@router.post("/optimize-paths")
async def optimize_flight_paths(
    targets: List[Target],
    drones: List[DroneSpec],
    algorithm: str = "genetic",
    max_iterations: int = 1000,
    constraints: Optional[Dict[str, Any]] = None,
    optimizer_manager: OptimizerManager = Depends(get_optimizer_manager)
):
    """优化飞行路径"""
    try:
        import time
        start_time = time.time()

        # 执行路径优化
        solution = await optimizer_manager.optimize_paths(
            targets=targets,
            drones=drones,
            algorithm=algorithm,
            constraints=constraints,
            max_iterations=max_iterations
        )

        execution_time = time.time() - start_time

        # 计算改进百分比（与简单分配相比）
        simple_distance = len(targets) * 1000  # 简单估算
        improvement = ((simple_distance - solution.total_distance) / simple_distance * 100) if simple_distance > 0 else 0

        optimization_result = {
            "algorithm": algorithm,
            "solution": {
                "paths": solution.paths,
                "total_distance": solution.total_distance,
                "max_distance": solution.max_distance,
                "fitness": solution.fitness,
                "is_valid": solution.is_valid
            },
            "improvement": f"{improvement:.1f}%",
            "execution_time": f"{execution_time:.2f}s",
            "iterations": max_iterations
        }

        logger.info(f"路径优化完成: 算法={algorithm}, 总距离={solution.total_distance:.2f}m")
        return optimization_result

    except Exception as e:
        logger.error(f"路径优化失败: {e}")
        raise HTTPException(status_code=500, detail=f"路径优化失败: {str(e)}")


@router.get("/algorithms")
async def list_algorithms(
    optimizer_manager: OptimizerManager = Depends(get_optimizer_manager)
):
    """获取可用的规划算法列表"""
    algorithm_info = optimizer_manager.get_algorithm_info()

    return {
        "path_planning": algorithm_info,
        "coverage_patterns": [
            {
                "name": "grid",
                "display_name": "网格覆盖",
                "description": "规则网格模式覆盖目标区域",
                "parameters": {
                    "width": "覆盖区域宽度（米）",
                    "height": "覆盖区域高度（米）",
                    "spacing": "网格间距（米）"
                }
            },
            {
                "name": "spiral",
                "display_name": "螺旋覆盖",
                "description": "从中心向外螺旋式覆盖",
                "parameters": {
                    "radius": "覆盖半径（米）",
                    "spacing": "螺旋间距（米）"
                }
            },
            {
                "name": "adaptive",
                "display_name": "自适应覆盖",
                "description": "根据目标密度自适应调整覆盖模式",
                "parameters": {
                    "coverage_radius": "单点覆盖半径（米）",
                    "targets": "目标列表"
                }
            }
        ]
    }


@router.post("/validate-mission")
async def validate_mission(
    mission_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """验证任务可行性"""
    try:
        # TODO: 实现任务验证逻辑
        # 1. 检查无人机能力约束
        # 2. 验证航线安全性
        # 3. 计算电池续航
        # 4. 检查空域冲突
        
        validation_result = {
            "mission_id": mission_id,
            "is_valid": True,
            "warnings": [
                "无人机2的电池可能不足以完成全部航线",
                "航线3与航线5存在潜在交叉点"
            ],
            "errors": [],
            "recommendations": [
                "建议在航点P5增加充电站",
                "调整航线3的高度避免冲突"
            ],
            "estimated_completion_time": "45分钟",
            "total_distance": 12500.0,
            "battery_usage": {
                "drone_1": 0.85,
                "drone_2": 0.95,
                "drone_3": 0.78
            }
        }
        
        logger.info(f"任务验证完成: {mission_id}")
        return validation_result
        
    except Exception as e:
        logger.error(f"任务验证失败: {e}")
        raise HTTPException(status_code=500, detail="任务验证失败")
