"""
Qwen3-30B 模型客户端
"""
import asyncio
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from openai import AsyncOpenAI
from loguru import logger

from app.config import get_settings
from app.models import PlanningRequest, PlanningResponse, Mission, Target, DroneSpec

settings = get_settings()


class QwenClient:
    """Qwen3-30B 模型客户端"""
    
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=settings.qwen_api_key,
            base_url=settings.qwen_api_base
        )
        self.model = settings.qwen_model
        
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        stream: bool = False,
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> str:
        """聊天完成"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=stream
            )
            
            if stream:
                return response  # 返回流对象
            else:
                return response.choices[0].message.content
                
        except Exception as e:
            logger.error(f"Qwen API调用失败: {e}")
            raise
            
    async def stream_chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> AsyncGenerator[str, None]:
        """流式聊天完成"""
        try:
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Qwen流式API调用失败: {e}")
            raise
            
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """你是一个专业的无人机任务规划AI助手，具备以下能力：

1. **任务分析**: 理解用户的侦察需求，分析目标特征和优先级
2. **资源评估**: 评估可用无人机的性能参数和适用性
3. **路径规划**: 生成高效的多无人机协同飞行路径
4. **风险评估**: 识别潜在的安全风险和约束条件
5. **方案优化**: 提供多种备选方案和优化建议

请始终以专业、准确、安全为原则，为用户提供最佳的任务规划方案。

回答格式要求：
- 使用中文回答
- 提供清晰的推理步骤
- 给出具体的数值参数
- 考虑实际操作的可行性
- 包含安全注意事项"""

    def _build_planning_prompt(
        self,
        request: PlanningRequest
    ) -> List[Dict[str, str]]:
        """构建规划提示词"""
        
        # 目标信息
        targets_info = ""
        if request.targets:
            targets_info = f"检测到 {len(request.targets)} 个目标：\n"
            for i, target in enumerate(request.targets[:5], 1):  # 限制显示前5个
                targets_info += f"  {i}. 类型: {target.type}, 位置: ({target.position.latitude:.6f}, {target.position.longitude:.6f}), 置信度: {target.confidence:.2f}\n"
            if len(request.targets) > 5:
                targets_info += f"  ... 还有 {len(request.targets) - 5} 个目标\n"
        
        # 无人机信息
        drones_info = ""
        if request.available_drones:
            drones_info = f"可用无人机 {len(request.available_drones)} 架：\n"
            for i, drone in enumerate(request.available_drones, 1):
                drones_info += f"  {i}. {drone.name} ({drone.model}): 最大速度 {drone.max_speed}m/s, 续航 {drone.battery_life/60:.0f}分钟, 最大高度 {drone.max_altitude}m\n"
        
        # 区域信息
        area_info = ""
        if request.area_center and request.area_radius:
            area_info = f"任务区域: 中心点 ({request.area_center.latitude:.6f}, {request.area_center.longitude:.6f}), 半径 {request.area_radius}m\n"
        
        # 约束条件
        constraints_info = ""
        if request.constraints:
            constraints_info = "约束条件:\n"
            for key, value in request.constraints.items():
                constraints_info += f"  - {key}: {value}\n"
        
        user_prompt = f"""用户请求: {request.message}

任务信息:
{targets_info}
{drones_info}
{area_info}
{constraints_info}

请基于以上信息，提供详细的任务规划方案，包括：
1. 任务分析和目标优先级
2. 无人机分配和角色划分
3. 飞行路径规划建议
4. 预估执行时间和资源消耗
5. 风险评估和安全建议
6. 备选方案（如有）

请确保方案的可行性和安全性。"""

        return [
            {"role": "system", "content": self._build_system_prompt()},
            {"role": "user", "content": user_prompt}
        ]
        
    async def plan_mission(self, request: PlanningRequest) -> PlanningResponse:
        """任务规划"""
        try:
            messages = self._build_planning_prompt(request)
            
            # 调用Qwen模型
            response_text = await self.chat_completion(messages, temperature=0.3)
            
            # 解析响应并提取关键信息
            reasoning_steps = self._extract_reasoning_steps(response_text)
            confidence_score = self._calculate_confidence_score(request, response_text)
            alternatives = self._extract_alternatives(response_text)
            
            # 尝试生成具体的任务方案
            suggested_mission = await self._generate_mission_from_response(
                request, response_text
            )
            
            return PlanningResponse(
                conversation_id=request.conversation_id,
                response_text=response_text,
                suggested_mission=suggested_mission,
                reasoning_steps=reasoning_steps,
                confidence_score=confidence_score,
                alternatives=alternatives
            )
            
        except Exception as e:
            logger.error(f"任务规划失败: {e}")
            # 返回错误响应
            return PlanningResponse(
                conversation_id=request.conversation_id,
                response_text=f"抱歉，任务规划过程中出现错误：{str(e)}。请检查输入参数并重试。",
                suggested_mission=None,
                reasoning_steps=["错误：无法完成任务规划"],
                confidence_score=0.0,
                alternatives=[]
            )
            
    async def stream_plan_mission(
        self, 
        request: PlanningRequest
    ) -> AsyncGenerator[str, None]:
        """流式任务规划"""
        try:
            messages = self._build_planning_prompt(request)
            
            async for chunk in self.stream_chat_completion(messages, temperature=0.3):
                yield chunk
                
        except Exception as e:
            logger.error(f"流式任务规划失败: {e}")
            yield f"错误：{str(e)}"
            
    def _extract_reasoning_steps(self, response_text: str) -> List[str]:
        """从响应中提取推理步骤"""
        steps = []
        lines = response_text.split('\n')
        
        for line in lines:
            line = line.strip()
            # 查找编号列表项
            if any(line.startswith(f"{i}.") for i in range(1, 10)):
                steps.append(line)
            # 查找带有特定关键词的行
            elif any(keyword in line for keyword in ["分析", "评估", "规划", "建议", "计算"]):
                if len(line) > 10:  # 过滤太短的行
                    steps.append(line)
                    
        return steps[:10]  # 限制最多10个步骤
        
    def _calculate_confidence_score(
        self, 
        request: PlanningRequest, 
        response_text: str
    ) -> float:
        """计算置信度分数"""
        score = 0.5  # 基础分数
        
        # 根据输入数据质量调整
        if request.targets:
            score += 0.1 * min(len(request.targets) / 10, 0.3)
        if request.available_drones:
            score += 0.1 * min(len(request.available_drones) / 5, 0.2)
        if request.area_center and request.area_radius:
            score += 0.1
            
        # 根据响应质量调整
        if len(response_text) > 500:
            score += 0.1
        if "风险" in response_text or "安全" in response_text:
            score += 0.05
        if "备选" in response_text or "方案" in response_text:
            score += 0.05
            
        return min(score, 1.0)
        
    def _extract_alternatives(self, response_text: str) -> List[str]:
        """从响应中提取备选方案"""
        alternatives = []
        lines = response_text.split('\n')
        
        in_alternatives_section = False
        for line in lines:
            line = line.strip()
            
            if "备选" in line or "方案" in line:
                in_alternatives_section = True
                continue
                
            if in_alternatives_section:
                if line and (line.startswith("-") or line.startswith("•") or 
                           any(line.startswith(f"{i}.") for i in range(1, 10))):
                    alternatives.append(line)
                elif line and not line.startswith(" "):
                    # 新的段落开始，结束备选方案提取
                    break
                    
        return alternatives[:5]  # 限制最多5个备选方案
        
    async def _generate_mission_from_response(
        self,
        request: PlanningRequest,
        response_text: str
    ) -> Optional[Mission]:
        """从响应中生成具体的任务方案"""
        try:
            # 这里可以进一步调用模型来生成结构化的任务数据
            # 或者使用规则解析响应文本
            
            if not request.area_center:
                return None
                
            from app.models import Mission, Coordinate
            import uuid
            
            mission = Mission(
                id=str(uuid.uuid4()),
                name=f"AI生成任务-{request.conversation_id[:8]}",
                description="基于AI分析生成的智能任务规划",
                target_ids=[target.id for target in request.targets],
                flight_paths=[],  # 需要进一步生成
                area_center=request.area_center,
                area_radius=request.area_radius or 5000.0,
                parameters={
                    "ai_generated": True,
                    "model": self.model,
                    "target_count": len(request.targets),
                    "drone_count": len(request.available_drones)
                }
            )
            
            return mission
            
        except Exception as e:
            logger.error(f"生成任务方案失败: {e}")
            return None


# 全局Qwen客户端实例
qwen_client = QwenClient()


async def get_qwen_client() -> QwenClient:
    """依赖注入：获取Qwen客户端"""
    return qwen_client
