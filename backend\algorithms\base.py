"""
算法基础类和工具函数
"""
import math
from abc import ABC, abstractmethod
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass

from app.models import Coordinate, Waypoint, FlightPath, DroneSpec, Target


@dataclass
class Point:
    """二维点"""
    x: float
    y: float
    
    def distance_to(self, other: 'Point') -> float:
        """计算到另一点的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


@dataclass
class PathSolution:
    """路径解决方案"""
    paths: List[List[int]]  # 每个无人机的路径（目标索引列表）
    total_distance: float
    max_distance: float  # 最长路径距离
    fitness: float
    is_valid: bool = True
    

class PathOptimizer(ABC):
    """路径优化器基类"""
    
    def __init__(self, targets: List[Target], drones: List[DroneSpec], constraints: Dict[str, Any]):
        self.targets = targets
        self.drones = drones
        self.constraints = constraints
        self.target_points = self._convert_targets_to_points()
        self.depot = Point(0, 0)  # 起始/结束点
        
    def _convert_targets_to_points(self) -> List[Point]:
        """将目标转换为二维点"""
        points = []
        for target in self.targets:
            # 简化：将经纬度转换为相对坐标
            x = (target.position.longitude - self.targets[0].position.longitude) * 111000
            y = (target.position.latitude - self.targets[0].position.latitude) * 111000
            points.append(Point(x, y))
        return points
        
    def calculate_distance_matrix(self) -> List[List[float]]:
        """计算距离矩阵"""
        n = len(self.target_points) + 1  # +1 for depot
        distance_matrix = [[0.0] * n for _ in range(n)]
        
        # 添加起始点
        all_points = [self.depot] + self.target_points
        
        for i in range(n):
            for j in range(n):
                if i != j:
                    distance_matrix[i][j] = all_points[i].distance_to(all_points[j])
                    
        return distance_matrix
        
    def evaluate_solution(self, solution: PathSolution) -> float:
        """评估解决方案"""
        if not solution.is_valid:
            return float('inf')
            
        # 多目标优化：最小化最大路径长度和总距离
        alpha = 0.7  # 最大路径权重
        beta = 0.3   # 总距离权重
        
        fitness = alpha * solution.max_distance + beta * solution.total_distance
        return fitness
        
    def calculate_path_distance(self, path: List[int], distance_matrix: List[List[float]]) -> float:
        """计算单条路径的距离"""
        if not path:
            return 0.0
            
        total_distance = 0.0
        current = 0  # 从起始点开始
        
        for target_idx in path:
            total_distance += distance_matrix[current][target_idx + 1]  # +1 因为起始点在索引0
            current = target_idx + 1
            
        # 返回起始点
        total_distance += distance_matrix[current][0]
        
        return total_distance
        
    def is_solution_valid(self, solution: PathSolution) -> bool:
        """检查解决方案是否有效"""
        # 检查所有目标是否被覆盖
        covered_targets = set()
        for path in solution.paths:
            covered_targets.update(path)
            
        if len(covered_targets) != len(self.targets):
            return False
            
        # 检查无人机约束
        distance_matrix = self.calculate_distance_matrix()
        for i, path in enumerate(solution.paths):
            if i >= len(self.drones):
                return False
                
            drone = self.drones[i]
            path_distance = self.calculate_path_distance(path, distance_matrix)
            
            # 检查续航约束
            estimated_time = path_distance / (drone.max_speed * 0.6)  # 平均速度
            if estimated_time > drone.battery_life:
                return False
                
        return True
        
    @abstractmethod
    def optimize(self, max_iterations: int = 1000) -> PathSolution:
        """优化路径"""
        pass


class GeographicUtils:
    """地理工具类"""
    
    @staticmethod
    def haversine_distance(coord1: Coordinate, coord2: Coordinate) -> float:
        """计算两点间的地理距离（米）"""
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(coord1.latitude)
        lat2_rad = math.radians(coord2.latitude)
        delta_lat = math.radians(coord2.latitude - coord1.latitude)
        delta_lon = math.radians(coord2.longitude - coord1.longitude)
        
        a = (math.sin(delta_lat / 2) ** 2 +
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
        
    @staticmethod
    def calculate_bearing(coord1: Coordinate, coord2: Coordinate) -> float:
        """计算方位角（度）"""
        lat1_rad = math.radians(coord1.latitude)
        lat2_rad = math.radians(coord2.latitude)
        delta_lon = math.radians(coord2.longitude - coord1.longitude)
        
        y = math.sin(delta_lon) * math.cos(lat2_rad)
        x = (math.cos(lat1_rad) * math.sin(lat2_rad) -
             math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(delta_lon))
        
        bearing_rad = math.atan2(y, x)
        bearing_deg = math.degrees(bearing_rad)
        
        return (bearing_deg + 360) % 360
        
    @staticmethod
    def destination_point(coord: Coordinate, distance: float, bearing: float) -> Coordinate:
        """根据起点、距离和方位角计算终点"""
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(coord.latitude)
        lon1_rad = math.radians(coord.longitude)
        bearing_rad = math.radians(bearing)
        
        lat2_rad = math.asin(
            math.sin(lat1_rad) * math.cos(distance / R) +
            math.cos(lat1_rad) * math.sin(distance / R) * math.cos(bearing_rad)
        )
        
        lon2_rad = lon1_rad + math.atan2(
            math.sin(bearing_rad) * math.sin(distance / R) * math.cos(lat1_rad),
            math.cos(distance / R) - math.sin(lat1_rad) * math.sin(lat2_rad)
        )
        
        return Coordinate(
            latitude=math.degrees(lat2_rad),
            longitude=math.degrees(lon2_rad),
            altitude=coord.altitude
        )


class CoveragePatternGenerator:
    """覆盖模式生成器"""
    
    @staticmethod
    def generate_grid_pattern(
        center: Coordinate,
        width: float,
        height: float,
        spacing: float,
        altitude: float = 100.0
    ) -> List[Waypoint]:
        """生成网格覆盖模式"""
        waypoints = []
        
        # 计算网格点数
        cols = int(width / spacing) + 1
        rows = int(height / spacing) + 1
        
        # 起始点
        start_lat = center.latitude - height / 2 / 111000
        start_lon = center.longitude - width / 2 / 111000
        
        for row in range(rows):
            for col in range(cols):
                # 蛇形路径：奇数行反向
                if row % 2 == 0:
                    actual_col = col
                else:
                    actual_col = cols - 1 - col
                    
                lat = start_lat + row * spacing / 111000
                lon = start_lon + actual_col * spacing / 111000
                
                waypoint = Waypoint(
                    position=Coordinate(latitude=lat, longitude=lon, altitude=altitude),
                    speed=15.0,
                    altitude=altitude,
                    action="SURVEY",
                    duration=30
                )
                waypoints.append(waypoint)
                
        return waypoints
        
    @staticmethod
    def generate_spiral_pattern(
        center: Coordinate,
        radius: float,
        spacing: float,
        altitude: float = 100.0
    ) -> List[Waypoint]:
        """生成螺旋覆盖模式"""
        waypoints = []
        
        angle = 0
        current_radius = spacing
        
        while current_radius <= radius:
            # 计算当前点
            point = GeographicUtils.destination_point(center, current_radius, angle)
            
            waypoint = Waypoint(
                position=Coordinate(
                    latitude=point.latitude,
                    longitude=point.longitude,
                    altitude=altitude
                ),
                speed=15.0,
                altitude=altitude,
                action="SURVEY",
                duration=30
            )
            waypoints.append(waypoint)
            
            # 更新角度和半径
            angle += 30  # 每次转30度
            if angle >= 360:
                angle = 0
                current_radius += spacing
                
        return waypoints
        
    @staticmethod
    def generate_adaptive_pattern(
        targets: List[Target],
        coverage_radius: float,
        altitude: float = 100.0
    ) -> List[Waypoint]:
        """生成自适应覆盖模式"""
        waypoints = []
        
        # 根据目标密度调整覆盖点
        for target in targets:
            # 在目标周围生成覆盖点
            for angle in range(0, 360, 90):  # 四个方向
                point = GeographicUtils.destination_point(
                    target.position, coverage_radius, angle
                )
                
                waypoint = Waypoint(
                    position=Coordinate(
                        latitude=point.latitude,
                        longitude=point.longitude,
                        altitude=altitude
                    ),
                    speed=12.0,
                    altitude=altitude,
                    action="SURVEY",
                    duration=45,
                    metadata={"target_id": target.id}
                )
                waypoints.append(waypoint)
                
        return waypoints
