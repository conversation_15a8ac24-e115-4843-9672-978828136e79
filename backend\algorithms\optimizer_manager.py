"""
优化算法管理器
"""
import asyncio
from typing import List, Dict, Any, Optional
from loguru import logger

from app.models import Target, DroneSpec, FlightPath, Waypoint, Coordinate
from algorithms.base import PathSolution, CoveragePatternGenerator, GeographicUtils
from algorithms.genetic_algorithm import GeneticAlgorithm
from algorithms.ant_colony import AntColonyOptimization
from algorithms.simulated_annealing import SimulatedAnnealing


class OptimizerManager:
    """优化算法管理器"""
    
    def __init__(self):
        self.available_algorithms = {
            'genetic': GeneticAlgorithm,
            'ant_colony': AntColonyOptimization,
            'simulated_annealing': SimulatedAnnealing
        }
        
    async def optimize_paths(
        self,
        targets: List[Target],
        drones: List[DroneSpec],
        algorithm: str = 'genetic',
        constraints: Optional[Dict[str, Any]] = None,
        max_iterations: int = 1000
    ) -> PathSolution:
        """优化路径"""
        try:
            if algorithm not in self.available_algorithms:
                raise ValueError(f"不支持的算法: {algorithm}")
                
            if not targets or not drones:
                raise ValueError("目标和无人机列表不能为空")
                
            # 设置默认约束
            if constraints is None:
                constraints = {
                    'default_altitude': 100.0,
                    'survey_altitude': 80.0,
                    'return_altitude': 120.0,
                    'survey_duration': 60,
                    'max_flight_time': 3600
                }
                
            logger.info(f"开始路径优化: 算法={algorithm}, 目标数={len(targets)}, 无人机数={len(drones)}")
            
            # 创建优化器
            optimizer_class = self.available_algorithms[algorithm]
            optimizer = optimizer_class(targets, drones, constraints)
            
            # 在线程池中运行优化（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            solution = await loop.run_in_executor(
                None, optimizer.optimize, max_iterations
            )
            
            logger.info(f"路径优化完成: 总距离={solution.total_distance:.2f}m, 最大距离={solution.max_distance:.2f}m")
            return solution
            
        except Exception as e:
            logger.error(f"路径优化失败: {e}")
            raise
            
    async def generate_flight_paths(
        self,
        solution: PathSolution,
        targets: List[Target],
        drones: List[DroneSpec],
        mission_center: Coordinate,
        constraints: Optional[Dict[str, Any]] = None
    ) -> List[FlightPath]:
        """根据优化解决方案生成飞行路径"""
        try:
            if constraints is None:
                constraints = {
                    'default_altitude': 100.0,
                    'survey_altitude': 80.0,
                    'return_altitude': 120.0,
                    'survey_duration': 60
                }
                
            flight_paths = []
            
            for drone_idx, path in enumerate(solution.paths):
                if drone_idx >= len(drones):
                    continue
                    
                drone = drones[drone_idx]
                
                # 生成航点
                waypoints = await self._generate_waypoints_for_path(
                    path, targets, drone, mission_center, constraints
                )
                
                # 计算路径统计
                total_distance = self._calculate_waypoint_distance(waypoints)
                estimated_duration = self._estimate_flight_duration(waypoints, drone)
                
                flight_path = FlightPath(
                    drone_id=drone.id,
                    waypoints=waypoints,
                    total_distance=total_distance,
                    estimated_duration=estimated_duration
                )
                
                flight_paths.append(flight_path)
                
            logger.info(f"生成飞行路径完成: {len(flight_paths)} 条路径")
            return flight_paths
            
        except Exception as e:
            logger.error(f"生成飞行路径失败: {e}")
            raise
            
    async def _generate_waypoints_for_path(
        self,
        target_indices: List[int],
        targets: List[Target],
        drone: DroneSpec,
        mission_center: Coordinate,
        constraints: Dict[str, Any]
    ) -> List[Waypoint]:
        """为路径生成航点"""
        waypoints = []
        
        # 起始点
        start_waypoint = Waypoint(
            position=mission_center,
            speed=drone.max_speed * 0.7,
            altitude=constraints.get('default_altitude', 100.0),
            action="TAKEOFF",
            duration=30,
            metadata={"phase": "takeoff"}
        )
        waypoints.append(start_waypoint)
        
        # 目标点
        for target_idx in target_indices:
            if target_idx < len(targets):
                target = targets[target_idx]
                
                target_waypoint = Waypoint(
                    position=target.position,
                    speed=drone.max_speed * 0.5,
                    altitude=constraints.get('survey_altitude', 80.0),
                    action="SURVEY",
                    duration=constraints.get('survey_duration', 60),
                    metadata={
                        "target_id": target.id,
                        "target_type": target.type,
                        "confidence": target.confidence,
                        "phase": "survey"
                    }
                )
                waypoints.append(target_waypoint)
                
        # 返回点
        return_waypoint = Waypoint(
            position=mission_center,
            speed=drone.max_speed * 0.8,
            altitude=constraints.get('return_altitude', 120.0),
            action="LANDING",
            duration=60,
            metadata={"phase": "landing"}
        )
        waypoints.append(return_waypoint)
        
        return waypoints
        
    def _calculate_waypoint_distance(self, waypoints: List[Waypoint]) -> float:
        """计算航点间总距离"""
        total_distance = 0.0
        
        for i in range(1, len(waypoints)):
            prev_pos = waypoints[i-1].position
            curr_pos = waypoints[i].position
            
            distance = GeographicUtils.haversine_distance(prev_pos, curr_pos)
            total_distance += distance
            
        return total_distance
        
    def _estimate_flight_duration(self, waypoints: List[Waypoint], drone: DroneSpec) -> int:
        """估算飞行时间"""
        total_time = 0
        
        # 飞行时间
        total_distance = self._calculate_waypoint_distance(waypoints)
        avg_speed = drone.max_speed * 0.6  # 平均速度
        flight_time = total_distance / avg_speed if avg_speed > 0 else 0
        
        # 任务时间
        task_time = sum(wp.duration for wp in waypoints)
        
        total_time = flight_time + task_time
        return int(total_time)
        
    async def generate_coverage_pattern(
        self,
        pattern_type: str,
        center: Coordinate,
        area_params: Dict[str, Any],
        altitude: float = 100.0
    ) -> List[Waypoint]:
        """生成覆盖模式"""
        try:
            if pattern_type == 'grid':
                return CoveragePatternGenerator.generate_grid_pattern(
                    center=center,
                    width=area_params.get('width', 1000),
                    height=area_params.get('height', 1000),
                    spacing=area_params.get('spacing', 100),
                    altitude=altitude
                )
            elif pattern_type == 'spiral':
                return CoveragePatternGenerator.generate_spiral_pattern(
                    center=center,
                    radius=area_params.get('radius', 500),
                    spacing=area_params.get('spacing', 100),
                    altitude=altitude
                )
            elif pattern_type == 'adaptive':
                targets = area_params.get('targets', [])
                return CoveragePatternGenerator.generate_adaptive_pattern(
                    targets=targets,
                    coverage_radius=area_params.get('coverage_radius', 50),
                    altitude=altitude
                )
            else:
                raise ValueError(f"不支持的覆盖模式: {pattern_type}")
                
        except Exception as e:
            logger.error(f"生成覆盖模式失败: {e}")
            raise
            
    async def validate_flight_paths(
        self,
        flight_paths: List[FlightPath],
        drones: List[DroneSpec],
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """验证飞行路径"""
        try:
            validation_result = {
                'is_valid': True,
                'warnings': [],
                'errors': [],
                'recommendations': [],
                'statistics': {}
            }
            
            if constraints is None:
                constraints = {'max_flight_time': 3600, 'min_battery_reserve': 0.1}
                
            total_distance = 0
            max_duration = 0
            
            for i, flight_path in enumerate(flight_paths):
                if i >= len(drones):
                    validation_result['errors'].append(f"飞行路径 {i} 没有对应的无人机")
                    validation_result['is_valid'] = False
                    continue
                    
                drone = drones[i]
                
                # 检查续航时间
                if flight_path.estimated_duration > drone.battery_life * (1 - constraints.get('min_battery_reserve', 0.1)):
                    validation_result['warnings'].append(
                        f"无人机 {drone.name} 的续航时间可能不足"
                    )
                    validation_result['recommendations'].append(
                        f"建议为无人机 {drone.name} 增加充电点或缩短路径"
                    )
                    
                # 检查飞行高度
                for waypoint in flight_path.waypoints:
                    if waypoint.altitude > drone.max_altitude:
                        validation_result['errors'].append(
                            f"无人机 {drone.name} 的航点高度超过最大飞行高度"
                        )
                        validation_result['is_valid'] = False
                        
                # 检查速度
                for waypoint in flight_path.waypoints:
                    if waypoint.speed > drone.max_speed:
                        validation_result['warnings'].append(
                            f"无人机 {drone.name} 的航点速度超过最大速度"
                        )
                        
                total_distance += flight_path.total_distance
                max_duration = max(max_duration, flight_path.estimated_duration)
                
            # 统计信息
            validation_result['statistics'] = {
                'total_distance': total_distance,
                'max_duration': max_duration,
                'average_distance': total_distance / len(flight_paths) if flight_paths else 0,
                'path_count': len(flight_paths)
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"验证飞行路径失败: {e}")
            return {
                'is_valid': False,
                'warnings': [],
                'errors': [f"验证过程出错: {str(e)}"],
                'recommendations': [],
                'statistics': {}
            }
            
    def get_algorithm_info(self) -> Dict[str, Any]:
        """获取算法信息"""
        return {
            'genetic': {
                'name': '遗传算法',
                'description': '基于进化计算的全局优化算法，适合复杂多目标优化',
                'parameters': {
                    'population_size': '种群大小（默认100）',
                    'mutation_rate': '变异率（默认0.1）',
                    'crossover_rate': '交叉率（默认0.8）',
                    'elite_size': '精英个体数量（默认20）'
                },
                'pros': ['全局搜索能力强', '适合多目标优化', '鲁棒性好'],
                'cons': ['收敛速度较慢', '参数调节复杂']
            },
            'ant_colony': {
                'name': '蚁群算法',
                'description': '模拟蚂蚁觅食行为的启发式算法，适合路径规划问题',
                'parameters': {
                    'num_ants': '蚂蚁数量（默认50）',
                    'alpha': '信息素重要程度（默认1.0）',
                    'beta': '启发式因子重要程度（默认2.0）',
                    'rho': '信息素挥发率（默认0.1）'
                },
                'pros': ['适合路径问题', '正反馈机制', '分布式计算'],
                'cons': ['容易陷入局部最优', '参数敏感']
            },
            'simulated_annealing': {
                'name': '模拟退火算法',
                'description': '模拟金属退火过程的优化算法，能跳出局部最优',
                'parameters': {
                    'initial_temperature': '初始温度（默认1000.0）',
                    'cooling_rate': '冷却速率（默认0.95）',
                    'min_temperature': '最低温度（默认1.0）'
                },
                'pros': ['能跳出局部最优', '实现简单', '内存需求小'],
                'cons': ['收敛速度慢', '温度参数难调节']
            }
        }


# 全局优化器管理器实例
optimizer_manager = OptimizerManager()


async def get_optimizer_manager() -> OptimizerManager:
    """依赖注入：获取优化器管理器"""
    return optimizer_manager
