"""
蚁群算法实现
"""
import random
import math
from typing import List, Dict, Any, Tu<PERSON>
from loguru import logger

from algorithms.base import PathOptimizer, PathSolution
from app.models import Target, DroneSpec


class AntColonyOptimization(PathOptimizer):
    """蚁群算法路径优化器"""
    
    def __init__(
        self,
        targets: List[Target],
        drones: List[DroneSpec],
        constraints: Dict[str, Any],
        num_ants: int = 50,
        alpha: float = 1.0,  # 信息素重要程度
        beta: float = 2.0,   # 启发式因子重要程度
        rho: float = 0.1,    # 信息素挥发率
        q: float = 100.0     # 信息素强度
    ):
        super().__init__(targets, drones, constraints)
        self.num_ants = num_ants
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.q = q
        self.distance_matrix = self.calculate_distance_matrix()
        self.pheromone_matrix = self._initialize_pheromone_matrix()
        
    def optimize(self, max_iterations: int = 1000) -> PathSolution:
        """使用蚁群算法优化路径"""
        try:
            logger.info(f"开始蚁群算法优化: 目标数={len(self.targets)}, 无人机数={len(self.drones)}")
            
            best_solution = None
            best_fitness = float('inf')
            
            for iteration in range(max_iterations):
                # 构建蚂蚁解决方案
                ant_solutions = []
                
                for ant in range(self.num_ants):
                    solution = self._construct_ant_solution()
                    ant_solutions.append(solution)
                    
                    # 更新最佳解决方案
                    if solution.fitness < best_fitness:
                        best_fitness = solution.fitness
                        best_solution = solution
                        
                # 更新信息素
                self._update_pheromones(ant_solutions)
                
                # 记录进度
                if iteration % 100 == 0:
                    logger.debug(f"蚁群算法第 {iteration} 次迭代，最佳适应度: {best_fitness:.2f}")
                    
            logger.info(f"蚁群算法优化完成，最佳适应度: {best_fitness:.2f}")
            return best_solution
            
        except Exception as e:
            logger.error(f"蚁群算法优化失败: {e}")
            return self._create_fallback_solution()
            
    def _initialize_pheromone_matrix(self) -> List[List[float]]:
        """初始化信息素矩阵"""
        n = len(self.targets) + 1  # +1 for depot
        initial_pheromone = 1.0
        
        pheromone_matrix = [[initial_pheromone for _ in range(n)] for _ in range(n)]
        return pheromone_matrix
        
    def _construct_ant_solution(self) -> PathSolution:
        """构建蚂蚁解决方案"""
        # 为每个无人机构建路径
        paths = []
        all_targets = set(range(len(self.targets)))
        
        for drone_idx in range(len(self.drones)):
            if not all_targets:
                paths.append([])
                continue
                
            # 为当前无人机构建路径
            path = self._construct_drone_path(all_targets, drone_idx)
            paths.append(path)
            
            # 移除已访问的目标
            all_targets -= set(path)
            
        # 处理剩余目标（分配给负载最轻的无人机）
        if all_targets:
            remaining_targets = list(all_targets)
            # 简单分配给第一个无人机
            if paths:
                paths[0].extend(remaining_targets)
            else:
                paths.append(remaining_targets)
                
        # 计算解决方案质量
        solution = self._evaluate_paths(paths)
        return solution
        
    def _construct_drone_path(self, available_targets: set, drone_idx: int) -> List[int]:
        """为单个无人机构建路径"""
        path = []
        current_position = 0  # 起始点
        remaining_targets = available_targets.copy()
        drone = self.drones[drone_idx]
        
        # 估算剩余续航
        remaining_battery = drone.battery_life
        
        while remaining_targets and remaining_battery > 0:
            # 计算到各目标的概率
            probabilities = self._calculate_transition_probabilities(
                current_position, remaining_targets
            )
            
            if not probabilities:
                break
                
            # 选择下一个目标
            next_target = self._select_next_target(probabilities)
            
            # 检查续航约束
            distance_to_target = self.distance_matrix[current_position][next_target + 1]
            distance_to_depot = self.distance_matrix[next_target + 1][0]
            total_distance = distance_to_target + distance_to_depot
            
            estimated_time = total_distance / (drone.max_speed * 0.6)
            
            if estimated_time > remaining_battery:
                break
                
            # 添加目标到路径
            path.append(next_target)
            remaining_targets.remove(next_target)
            current_position = next_target + 1
            remaining_battery -= estimated_time
            
        return path
        
    def _calculate_transition_probabilities(
        self,
        current_position: int,
        available_targets: set
    ) -> Dict[int, float]:
        """计算转移概率"""
        probabilities = {}
        
        for target in available_targets:
            target_position = target + 1  # +1 for depot offset
            
            # 信息素浓度
            pheromone = self.pheromone_matrix[current_position][target_position]
            
            # 启发式信息（距离的倒数）
            distance = self.distance_matrix[current_position][target_position]
            heuristic = 1.0 / (distance + 1e-10)  # 避免除零
            
            # 计算概率
            probability = (pheromone ** self.alpha) * (heuristic ** self.beta)
            probabilities[target] = probability
            
        # 归一化概率
        total_prob = sum(probabilities.values())
        if total_prob > 0:
            for target in probabilities:
                probabilities[target] /= total_prob
                
        return probabilities
        
    def _select_next_target(self, probabilities: Dict[int, float]) -> int:
        """基于概率选择下一个目标"""
        if not probabilities:
            return None
            
        # 轮盘赌选择
        rand_value = random.random()
        cumulative_prob = 0.0
        
        for target, prob in probabilities.items():
            cumulative_prob += prob
            if rand_value <= cumulative_prob:
                return target
                
        # 如果没有选中，返回最后一个
        return list(probabilities.keys())[-1]
        
    def _evaluate_paths(self, paths: List[List[int]]) -> PathSolution:
        """评估路径质量"""
        path_distances = []
        
        for path in paths:
            distance = self.calculate_path_distance(path, self.distance_matrix)
            path_distances.append(distance)
            
        total_distance = sum(path_distances)
        max_distance = max(path_distances) if path_distances else 0
        
        solution = PathSolution(
            paths=paths,
            total_distance=total_distance,
            max_distance=max_distance,
            fitness=0.0
        )
        
        solution.is_valid = self.is_solution_valid(solution)
        solution.fitness = self.evaluate_solution(solution)
        
        return solution
        
    def _update_pheromones(self, ant_solutions: List[PathSolution]):
        """更新信息素"""
        # 信息素挥发
        for i in range(len(self.pheromone_matrix)):
            for j in range(len(self.pheromone_matrix[i])):
                self.pheromone_matrix[i][j] *= (1 - self.rho)
                
        # 添加新信息素
        for solution in ant_solutions:
            if solution.is_valid:
                pheromone_deposit = self.q / solution.fitness
                
                for path in solution.paths:
                    self._deposit_pheromone_on_path(path, pheromone_deposit)
                    
    def _deposit_pheromone_on_path(self, path: List[int], pheromone_amount: float):
        """在路径上沉积信息素"""
        if not path:
            return
            
        current = 0  # 起始点
        
        for target in path:
            target_position = target + 1
            self.pheromone_matrix[current][target_position] += pheromone_amount
            self.pheromone_matrix[target_position][current] += pheromone_amount
            current = target_position
            
        # 返回起始点
        self.pheromone_matrix[current][0] += pheromone_amount
        self.pheromone_matrix[0][current] += pheromone_amount
        
    def _create_fallback_solution(self) -> PathSolution:
        """创建备用解决方案"""
        # 简单的轮询分配
        paths = [[] for _ in range(len(self.drones))]
        
        for i, target_idx in enumerate(range(len(self.targets))):
            drone_idx = i % len(self.drones)
            paths[drone_idx].append(target_idx)
            
        return self._evaluate_paths(paths)
