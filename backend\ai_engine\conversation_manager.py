"""
对话管理器
"""
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from loguru import logger

from app.models import ConversationDB, PlanningRequest, PlanningResponse
from database.connection import get_pg_session


class ConversationManager:
    """对话管理器"""
    
    def __init__(self):
        self.max_history_length = 20  # 最大历史记录长度
        
    async def create_conversation(
        self,
        user_id: str,
        conversation_id: str,
        db: AsyncSession
    ) -> str:
        """创建新对话"""
        try:
            conversation = ConversationDB(
                id=conversation_id,
                user_id=user_id,
                messages=[]
            )
            
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)
            
            logger.info(f"创建对话: {conversation_id}, 用户: {user_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"创建对话失败: {e}")
            raise
            
    async def add_message(
        self,
        conversation_id: str,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        db: AsyncSession = None
    ):
        """添加消息到对话"""
        if db is None:
            async with get_pg_session() as session:
                await self._add_message_impl(conversation_id, role, content, metadata, session)
        else:
            await self._add_message_impl(conversation_id, role, content, metadata, db)
            
    async def _add_message_impl(
        self,
        conversation_id: str,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]],
        db: AsyncSession
    ):
        """添加消息实现"""
        try:
            # 获取对话
            query = select(ConversationDB).where(ConversationDB.id == conversation_id)
            result = await db.execute(query)
            conversation = result.scalar_one_or_none()
            
            if not conversation:
                # 如果对话不存在，创建新对话
                conversation = ConversationDB(
                    id=conversation_id,
                    user_id="unknown",
                    messages=[]
                )
                db.add(conversation)
            
            # 构建消息
            message = {
                "role": role,
                "content": content,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": metadata or {}
            }
            
            # 添加消息
            messages = conversation.messages or []
            messages.append(message)
            
            # 限制历史长度
            if len(messages) > self.max_history_length:
                messages = messages[-self.max_history_length:]
                
            # 更新对话
            conversation.messages = messages
            conversation.updated_at = datetime.utcnow()
            
            await db.commit()
            
            logger.debug(f"添加消息到对话 {conversation_id}: {role}")
            
        except Exception as e:
            logger.error(f"添加消息失败: {e}")
            raise
            
    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: Optional[int] = None,
        db: AsyncSession = None
    ) -> List[Dict[str, Any]]:
        """获取对话历史"""
        if db is None:
            async with get_pg_session() as session:
                return await self._get_conversation_history_impl(conversation_id, limit, session)
        else:
            return await self._get_conversation_history_impl(conversation_id, limit, db)
            
    async def _get_conversation_history_impl(
        self,
        conversation_id: str,
        limit: Optional[int],
        db: AsyncSession
    ) -> List[Dict[str, Any]]:
        """获取对话历史实现"""
        try:
            query = select(ConversationDB).where(ConversationDB.id == conversation_id)
            result = await db.execute(query)
            conversation = result.scalar_one_or_none()
            
            if not conversation:
                return []
                
            messages = conversation.messages or []
            
            if limit:
                messages = messages[-limit:]
                
            return messages
            
        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return []
            
    async def format_messages_for_ai(
        self,
        conversation_id: str,
        include_system: bool = True,
        db: AsyncSession = None
    ) -> List[Dict[str, str]]:
        """格式化消息供AI模型使用"""
        try:
            messages = await self.get_conversation_history(conversation_id, db=db)
            
            formatted_messages = []
            
            if include_system:
                formatted_messages.append({
                    "role": "system",
                    "content": self._get_system_message()
                })
            
            for msg in messages:
                if msg["role"] in ["user", "assistant"]:
                    formatted_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                    
            return formatted_messages
            
        except Exception as e:
            logger.error(f"格式化消息失败: {e}")
            return []
            
    def _get_system_message(self) -> str:
        """获取系统消息"""
        return """你是一个专业的无人机任务规划AI助手。你的职责是：

1. 理解用户的侦察和监控需求
2. 分析目标区域和目标特征
3. 评估可用的无人机资源
4. 生成高效的飞行路径规划
5. 提供安全和风险评估
6. 优化多无人机协同作业方案

请始终保持专业、准确、安全的原则，为用户提供最佳的解决方案。使用中文进行交流。"""

    async def clear_conversation(
        self,
        conversation_id: str,
        db: AsyncSession = None
    ):
        """清空对话历史"""
        if db is None:
            async with get_pg_session() as session:
                await self._clear_conversation_impl(conversation_id, session)
        else:
            await self._clear_conversation_impl(conversation_id, db)
            
    async def _clear_conversation_impl(
        self,
        conversation_id: str,
        db: AsyncSession
    ):
        """清空对话历史实现"""
        try:
            update_query = update(ConversationDB).where(
                ConversationDB.id == conversation_id
            ).values(
                messages=[],
                updated_at=datetime.utcnow()
            )
            
            await db.execute(update_query)
            await db.commit()
            
            logger.info(f"清空对话历史: {conversation_id}")
            
        except Exception as e:
            logger.error(f"清空对话历史失败: {e}")
            raise
            
    async def get_conversation_summary(
        self,
        conversation_id: str,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """获取对话摘要"""
        try:
            messages = await self.get_conversation_history(conversation_id, db=db)
            
            if not messages:
                return {
                    "message_count": 0,
                    "first_message_time": None,
                    "last_message_time": None,
                    "user_messages": 0,
                    "assistant_messages": 0
                }
            
            user_messages = sum(1 for msg in messages if msg["role"] == "user")
            assistant_messages = sum(1 for msg in messages if msg["role"] == "assistant")
            
            return {
                "message_count": len(messages),
                "first_message_time": messages[0]["timestamp"] if messages else None,
                "last_message_time": messages[-1]["timestamp"] if messages else None,
                "user_messages": user_messages,
                "assistant_messages": assistant_messages
            }
            
        except Exception as e:
            logger.error(f"获取对话摘要失败: {e}")
            return {}
            
    async def export_conversation(
        self,
        conversation_id: str,
        format: str = "json",
        db: AsyncSession = None
    ) -> str:
        """导出对话"""
        try:
            messages = await self.get_conversation_history(conversation_id, db=db)
            
            if format == "json":
                return json.dumps(messages, ensure_ascii=False, indent=2)
            elif format == "text":
                text_lines = []
                for msg in messages:
                    timestamp = msg.get("timestamp", "")
                    role = msg.get("role", "")
                    content = msg.get("content", "")
                    text_lines.append(f"[{timestamp}] {role}: {content}")
                return "\n".join(text_lines)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            logger.error(f"导出对话失败: {e}")
            raise
            
    async def search_conversations(
        self,
        user_id: str,
        keyword: Optional[str] = None,
        limit: int = 10,
        db: AsyncSession = None
    ) -> List[Dict[str, Any]]:
        """搜索对话"""
        if db is None:
            async with get_pg_session() as session:
                return await self._search_conversations_impl(user_id, keyword, limit, session)
        else:
            return await self._search_conversations_impl(user_id, keyword, limit, db)
            
    async def _search_conversations_impl(
        self,
        user_id: str,
        keyword: Optional[str],
        limit: int,
        db: AsyncSession
    ) -> List[Dict[str, Any]]:
        """搜索对话实现"""
        try:
            query = select(ConversationDB).where(ConversationDB.user_id == user_id)
            
            if keyword:
                # 这里可以实现更复杂的搜索逻辑
                # 目前简单地在消息内容中搜索关键词
                pass
                
            query = query.order_by(ConversationDB.updated_at.desc()).limit(limit)
            
            result = await db.execute(query)
            conversations = result.scalars().all()
            
            conversation_list = []
            for conv in conversations:
                summary = await self.get_conversation_summary(conv.id, db)
                conversation_list.append({
                    "id": conv.id,
                    "user_id": conv.user_id,
                    "created_at": conv.created_at.isoformat(),
                    "updated_at": conv.updated_at.isoformat(),
                    "summary": summary
                })
                
            return conversation_list
            
        except Exception as e:
            logger.error(f"搜索对话失败: {e}")
            return []


# 全局对话管理器实例
conversation_manager = ConversationManager()


async def get_conversation_manager() -> ConversationManager:
    """依赖注入：获取对话管理器"""
    return conversation_manager
