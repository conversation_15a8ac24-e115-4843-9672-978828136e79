"""
知识库管理路由
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
from loguru import logger

from ai_engine.rag_service import get_rag_service, RAGService

router = APIRouter()


class KnowledgeItem(BaseModel):
    """知识项模型"""
    id: Optional[str] = None
    type: str
    title: str
    content: str
    keywords: List[str] = []
    metadata: Dict[str, Any] = {}


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str
    limit: int = 5
    score_threshold: float = 0.7
    knowledge_type: Optional[str] = None


class SearchResult(BaseModel):
    """搜索结果模型"""
    id: str
    score: float
    knowledge: Dict[str, Any]


@router.post("/knowledge", response_model=Dict[str, str])
async def add_knowledge(
    knowledge: KnowledgeItem,
    rag_service: RAGService = Depends(get_rag_service)
):
    """添加知识到知识库"""
    try:
        knowledge_data = knowledge.dict()
        knowledge_id = await rag_service.add_knowledge(knowledge_data)
        
        logger.info(f"添加知识成功: {knowledge_id}")
        return {"knowledge_id": knowledge_id, "message": "知识添加成功"}
        
    except Exception as e:
        logger.error(f"添加知识失败: {e}")
        raise HTTPException(status_code=500, detail="添加知识失败")


@router.post("/knowledge/batch", response_model=Dict[str, Any])
async def add_knowledge_batch(
    knowledge_list: List[KnowledgeItem],
    rag_service: RAGService = Depends(get_rag_service)
):
    """批量添加知识"""
    try:
        added_ids = []
        failed_count = 0
        
        for knowledge in knowledge_list:
            try:
                knowledge_data = knowledge.dict()
                knowledge_id = await rag_service.add_knowledge(knowledge_data)
                added_ids.append(knowledge_id)
            except Exception as e:
                logger.error(f"添加单个知识失败: {e}")
                failed_count += 1
                
        logger.info(f"批量添加知识完成: 成功 {len(added_ids)}, 失败 {failed_count}")
        return {
            "added_count": len(added_ids),
            "failed_count": failed_count,
            "added_ids": added_ids,
            "message": f"批量添加完成，成功 {len(added_ids)} 条，失败 {failed_count} 条"
        }
        
    except Exception as e:
        logger.error(f"批量添加知识失败: {e}")
        raise HTTPException(status_code=500, detail="批量添加知识失败")


@router.post("/search", response_model=List[SearchResult])
async def search_knowledge(
    search_request: SearchRequest,
    rag_service: RAGService = Depends(get_rag_service)
):
    """搜索知识库"""
    try:
        results = await rag_service.search_knowledge(
            query=search_request.query,
            limit=search_request.limit,
            score_threshold=search_request.score_threshold,
            knowledge_type=search_request.knowledge_type
        )
        
        search_results = [
            SearchResult(
                id=result["id"],
                score=result["score"],
                knowledge=result["knowledge"]
            )
            for result in results
        ]
        
        logger.info(f"知识搜索完成: 查询='{search_request.query}', 结果数={len(search_results)}")
        return search_results
        
    except Exception as e:
        logger.error(f"知识搜索失败: {e}")
        raise HTTPException(status_code=500, detail="知识搜索失败")


@router.get("/context")
async def get_relevant_context(
    query: str = Query(..., description="查询文本"),
    context_types: Optional[List[str]] = Query(None, description="上下文类型列表"),
    rag_service: RAGService = Depends(get_rag_service)
):
    """获取相关上下文"""
    try:
        context = await rag_service.get_relevant_context(
            query=query,
            context_types=context_types
        )
        
        return {
            "query": query,
            "context": context,
            "context_types": context_types
        }
        
    except Exception as e:
        logger.error(f"获取相关上下文失败: {e}")
        raise HTTPException(status_code=500, detail="获取相关上下文失败")


@router.get("/recommendations/drones")
async def get_drone_recommendations(
    mission_type: str = Query(..., description="任务类型"),
    area_size: float = Query(..., description="区域大小(平方米)"),
    duration: Optional[int] = Query(None, description="预计时长(分钟)"),
    payload_required: Optional[float] = Query(None, description="载荷需求(kg)"),
    rag_service: RAGService = Depends(get_rag_service)
):
    """获取无人机推荐"""
    try:
        requirements = {
            "mission_type": mission_type,
            "area_size": area_size
        }
        
        if duration:
            requirements["duration"] = duration
        if payload_required:
            requirements["payload_required"] = payload_required
            
        recommendations = await rag_service.get_drone_recommendations(requirements)
        
        return {
            "requirements": requirements,
            "recommendations": recommendations,
            "count": len(recommendations)
        }
        
    except Exception as e:
        logger.error(f"获取无人机推荐失败: {e}")
        raise HTTPException(status_code=500, detail="获取无人机推荐失败")


@router.get("/recommendations/patterns")
async def get_mission_patterns(
    mission_type: str = Query(..., description="任务类型"),
    area_size: float = Query(..., description="区域大小(平方米)"),
    rag_service: RAGService = Depends(get_rag_service)
):
    """获取任务模式建议"""
    try:
        patterns = await rag_service.get_mission_patterns(mission_type, area_size)
        
        return {
            "mission_type": mission_type,
            "area_size": area_size,
            "patterns": patterns,
            "count": len(patterns)
        }
        
    except Exception as e:
        logger.error(f"获取任务模式建议失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务模式建议失败")


@router.delete("/knowledge/{knowledge_id}")
async def delete_knowledge(
    knowledge_id: str,
    rag_service: RAGService = Depends(get_rag_service)
):
    """删除知识"""
    try:
        await rag_service.delete_knowledge(knowledge_id)
        
        logger.info(f"删除知识成功: {knowledge_id}")
        return {"message": "知识删除成功"}
        
    except Exception as e:
        logger.error(f"删除知识失败: {e}")
        raise HTTPException(status_code=500, detail="删除知识失败")


@router.get("/stats")
async def get_knowledge_stats(
    rag_service: RAGService = Depends(get_rag_service)
):
    """获取知识库统计信息"""
    try:
        stats = await rag_service.get_knowledge_stats()
        
        return {
            "stats": stats,
            "message": "获取统计信息成功"
        }
        
    except Exception as e:
        logger.error(f"获取知识库统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取知识库统计失败")


@router.post("/initialize")
async def initialize_knowledge_base(
    rag_service: RAGService = Depends(get_rag_service)
):
    """初始化知识库"""
    try:
        await rag_service.initialize()
        
        return {"message": "知识库初始化成功"}
        
    except Exception as e:
        logger.error(f"初始化知识库失败: {e}")
        raise HTTPException(status_code=500, detail="初始化知识库失败")


@router.post("/mission-experience")
async def add_mission_experience(
    mission_id: str,
    mission_type: str,
    success: bool,
    lessons_learned: str,
    parameters: Dict[str, Any] = {},
    rag_service: RAGService = Depends(get_rag_service)
):
    """添加任务经验"""
    try:
        await rag_service.add_mission_experience(
            mission_id=mission_id,
            mission_type=mission_type,
            success=success,
            lessons_learned=lessons_learned,
            parameters=parameters
        )
        
        logger.info(f"添加任务经验成功: {mission_id}")
        return {"message": "任务经验添加成功"}
        
    except Exception as e:
        logger.error(f"添加任务经验失败: {e}")
        raise HTTPException(status_code=500, detail="添加任务经验失败")


@router.get("/types")
async def get_knowledge_types():
    """获取知识类型列表"""
    return {
        "knowledge_types": [
            {
                "type": "drone_knowledge",
                "name": "无人机知识",
                "description": "无人机基础知识和特性"
            },
            {
                "type": "drone_spec",
                "name": "无人机规格",
                "description": "具体无人机型号的技术规格"
            },
            {
                "type": "mission_pattern",
                "name": "任务模式",
                "description": "各种任务执行模式和策略"
            },
            {
                "type": "mission_experience",
                "name": "任务经验",
                "description": "历史任务的执行经验和教训"
            },
            {
                "type": "safety_rule",
                "name": "安全规则",
                "description": "飞行安全规则和注意事项"
            },
            {
                "type": "weather_condition",
                "name": "天气条件",
                "description": "天气对飞行的影响和应对策略"
            }
        ]
    }
