version: '3.8'

services:
  # PostgreSQL 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: mps_postgres
    environment:
      POSTGRES_DB: missionplanning
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deployment/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - mps_network

  # TimescaleDB 时间序列数据库
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: mps_timescaledb
    environment:
      POSTGRES_DB: timescale
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
    networks:
      - mps_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: mps_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mps_network

  # Qdrant 向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    container_name: mps_qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    networks:
      - mps_network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: mps_minio
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    networks:
      - mps_network

  # 后端 FastAPI 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: mps_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/missionplanning
      - TIMESCALE_URL=postgresql+asyncpg://postgres:password@timescaledb:5432/timescale
      - REDIS_URL=redis://redis:6379/0
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - MINIO_ENDPOINT=minio:9000
    depends_on:
      - postgres
      - timescaledb
      - redis
      - qdrant
      - minio
    volumes:
      - ./backend:/app
    networks:
      - mps_network
    restart: unless-stopped

  # Cesium 可视化服务
  cesium_service:
    build:
      context: ./backend/visualization
      dockerfile: Dockerfile
    container_name: mps_cesium
    ports:
      - "50051:50051"
    environment:
      - GRPC_PORT=50051
      - CESIUM_ION_ACCESS_TOKEN=${CESIUM_ION_ACCESS_TOKEN}
    networks:
      - mps_network
    restart: unless-stopped

  # 前端 Vue 应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: mps_frontend
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000/ws
      - VITE_CESIUM_ION_ACCESS_TOKEN=${CESIUM_ION_ACCESS_TOKEN}
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - mps_network
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: mps_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - mps_network
    restart: unless-stopped

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: mps_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - mps_network

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: mps_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./deployment/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - mps_network

volumes:
  postgres_data:
  timescale_data:
  redis_data:
  qdrant_data:
  minio_data:
  prometheus_data:
  grafana_data:

networks:
  mps_network:
    driver: bridge
