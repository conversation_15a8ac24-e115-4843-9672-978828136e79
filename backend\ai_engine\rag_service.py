"""
RAG (检索增强生成) 服务
"""
import json
import uuid
from typing import List, Dict, Any, Optional, Tuple
from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient
from qdrant_client.http import models
from loguru import logger

from app.config import get_settings
from app.models import DroneSpec, Target

settings = get_settings()


class RAGService:
    """RAG服务"""
    
    def __init__(self):
        self.qdrant_client = None
        self.embedding_model = None
        self.collection_name = settings.qdrant_collection_name
        self.embedding_dim = 384  # all-MiniLM-L6-v2 的维度
        
    async def initialize(self):
        """初始化RAG服务"""
        try:
            # 初始化Qdrant客户端
            self.qdrant_client = QdrantClient(
                host=settings.qdrant_host,
                port=settings.qdrant_port,
                api_key=settings.qdrant_api_key
            )
            
            # 初始化嵌入模型
            self.embedding_model = SentenceTransformer(settings.embedding_model)
            
            # 确保集合存在
            await self._ensure_collection_exists()
            
            logger.info("RAG服务初始化完成")
            
        except Exception as e:
            logger.error(f"RAG服务初始化失败: {e}")
            raise
            
    async def _ensure_collection_exists(self):
        """确保集合存在"""
        try:
            collections = self.qdrant_client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            if self.collection_name not in collection_names:
                self.qdrant_client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=models.VectorParams(
                        size=self.embedding_dim,
                        distance=models.Distance.COSINE
                    )
                )
                logger.info(f"创建Qdrant集合: {self.collection_name}")
                
                # 初始化基础知识库
                await self._initialize_knowledge_base()
                
        except Exception as e:
            logger.error(f"确保集合存在失败: {e}")
            raise
            
    async def _initialize_knowledge_base(self):
        """初始化基础知识库"""
        try:
            # 无人机基础知识
            drone_knowledge = [
                {
                    "id": "drone_basic_1",
                    "type": "drone_knowledge",
                    "title": "多旋翼无人机特性",
                    "content": "多旋翼无人机具有垂直起降能力，悬停稳定性好，适合精确定点侦察和近距离目标监控。典型续航时间20-40分钟，有效载荷1-5kg。",
                    "keywords": ["多旋翼", "悬停", "垂直起降", "精确定点", "侦察"]
                },
                {
                    "id": "drone_basic_2", 
                    "type": "drone_knowledge",
                    "title": "固定翼无人机特性",
                    "content": "固定翼无人机具有长航时、大航程优势，适合大面积区域巡逻和长距离侦察。典型续航时间2-8小时，巡航速度50-150km/h。",
                    "keywords": ["固定翼", "长航时", "大航程", "区域巡逻", "长距离"]
                },
                {
                    "id": "mission_pattern_1",
                    "type": "mission_pattern",
                    "title": "网格搜索模式",
                    "content": "网格搜索是系统性覆盖目标区域的标准模式，确保无遗漏。适用于未知目标搜索、区域监控等任务。飞行路径规整，便于数据处理。",
                    "keywords": ["网格搜索", "系统覆盖", "无遗漏", "区域监控"]
                },
                {
                    "id": "mission_pattern_2",
                    "type": "mission_pattern", 
                    "title": "螺旋搜索模式",
                    "content": "螺旋搜索从中心点向外扩展，适用于已知大致位置的目标搜索。可以快速覆盖核心区域，逐步扩大搜索范围。",
                    "keywords": ["螺旋搜索", "中心扩展", "已知位置", "快速覆盖"]
                },
                {
                    "id": "safety_rule_1",
                    "type": "safety_rule",
                    "title": "飞行高度安全规则",
                    "content": "无人机飞行高度应保持在120米以下，避开民航航线。在建筑物密集区域，建议飞行高度不超过建筑物高度加30米。",
                    "keywords": ["飞行高度", "120米", "民航航线", "建筑物", "安全"]
                },
                {
                    "id": "weather_condition_1",
                    "type": "weather_condition",
                    "title": "风速对飞行的影响",
                    "content": "风速超过6级（10.8-13.8m/s）时，小型多旋翼无人机不宜飞行。风速超过4级时，需要调整飞行路径和速度，确保飞行稳定性。",
                    "keywords": ["风速", "6级风", "飞行稳定性", "路径调整"]
                }
            ]
            
            # 添加知识到向量数据库
            for knowledge in drone_knowledge:
                await self.add_knowledge(knowledge)
                
            logger.info(f"初始化知识库完成，添加了 {len(drone_knowledge)} 条知识")
            
        except Exception as e:
            logger.error(f"初始化知识库失败: {e}")
            
    async def add_knowledge(self, knowledge: Dict[str, Any]) -> str:
        """添加知识到向量数据库"""
        try:
            # 生成嵌入向量
            text_content = f"{knowledge.get('title', '')} {knowledge.get('content', '')}"
            embedding = self.embedding_model.encode(text_content).tolist()
            
            # 生成唯一ID
            knowledge_id = knowledge.get('id', str(uuid.uuid4()))
            
            # 添加到Qdrant
            self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=[
                    models.PointStruct(
                        id=knowledge_id,
                        vector=embedding,
                        payload=knowledge
                    )
                ]
            )
            
            logger.debug(f"添加知识: {knowledge_id}")
            return knowledge_id
            
        except Exception as e:
            logger.error(f"添加知识失败: {e}")
            raise
            
    async def search_knowledge(
        self,
        query: str,
        limit: int = 5,
        score_threshold: float = 0.7,
        knowledge_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            # 生成查询嵌入
            query_embedding = self.embedding_model.encode(query).tolist()
            
            # 构建过滤条件
            filter_conditions = None
            if knowledge_type:
                filter_conditions = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="type",
                            match=models.MatchValue(value=knowledge_type)
                        )
                    ]
                )
            
            # 搜索
            search_result = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                query_filter=filter_conditions,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # 格式化结果
            results = []
            for hit in search_result:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "knowledge": hit.payload
                }
                results.append(result)
                
            logger.debug(f"知识搜索: 查询='{query}', 结果数={len(results)}")
            return results
            
        except Exception as e:
            logger.error(f"知识搜索失败: {e}")
            return []
            
    async def get_relevant_context(
        self,
        query: str,
        context_types: Optional[List[str]] = None
    ) -> str:
        """获取相关上下文"""
        try:
            all_context = []
            
            # 如果指定了上下文类型，分别搜索
            if context_types:
                for context_type in context_types:
                    results = await self.search_knowledge(
                        query=query,
                        limit=3,
                        knowledge_type=context_type
                    )
                    all_context.extend(results)
            else:
                # 通用搜索
                results = await self.search_knowledge(query=query, limit=5)
                all_context.extend(results)
                
            # 格式化上下文
            context_text = ""
            for i, result in enumerate(all_context, 1):
                knowledge = result["knowledge"]
                context_text += f"\n{i}. {knowledge.get('title', '未知标题')}\n"
                context_text += f"   {knowledge.get('content', '无内容')}\n"
                
            return context_text.strip()
            
        except Exception as e:
            logger.error(f"获取相关上下文失败: {e}")
            return ""
            
    async def add_drone_spec_knowledge(self, drone_spec: DroneSpec):
        """添加无人机规格知识"""
        try:
            knowledge = {
                "id": f"drone_spec_{drone_spec.id}",
                "type": "drone_spec",
                "title": f"{drone_spec.name} ({drone_spec.model})",
                "content": f"无人机型号 {drone_spec.model}，最大速度 {drone_spec.max_speed}m/s，"
                          f"最大飞行高度 {drone_spec.max_altitude}m，续航时间 {drone_spec.battery_life/60:.1f}分钟，"
                          f"载荷容量 {drone_spec.payload_capacity}kg。"
                          f"具备能力：{', '.join(drone_spec.capabilities)}。",
                "keywords": [drone_spec.name, drone_spec.model] + drone_spec.capabilities,
                "drone_spec": drone_spec.dict()
            }
            
            await self.add_knowledge(knowledge)
            logger.info(f"添加无人机规格知识: {drone_spec.name}")
            
        except Exception as e:
            logger.error(f"添加无人机规格知识失败: {e}")
            
    async def add_mission_experience(
        self,
        mission_id: str,
        mission_type: str,
        success: bool,
        lessons_learned: str,
        parameters: Dict[str, Any]
    ):
        """添加任务经验"""
        try:
            knowledge = {
                "id": f"mission_exp_{mission_id}",
                "type": "mission_experience",
                "title": f"任务经验 - {mission_type}",
                "content": f"任务类型：{mission_type}，"
                          f"执行结果：{'成功' if success else '失败'}。"
                          f"经验总结：{lessons_learned}",
                "keywords": [mission_type, "经验", "总结"],
                "mission_data": {
                    "mission_id": mission_id,
                    "mission_type": mission_type,
                    "success": success,
                    "parameters": parameters
                }
            }
            
            await self.add_knowledge(knowledge)
            logger.info(f"添加任务经验: {mission_id}")
            
        except Exception as e:
            logger.error(f"添加任务经验失败: {e}")
            
    async def get_drone_recommendations(
        self,
        mission_requirements: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取无人机推荐"""
        try:
            # 构建查询
            requirements_text = " ".join([
                f"{key}: {value}" for key, value in mission_requirements.items()
            ])
            
            # 搜索相关无人机规格
            results = await self.search_knowledge(
                query=requirements_text,
                knowledge_type="drone_spec",
                limit=10
            )
            
            recommendations = []
            for result in results:
                if "drone_spec" in result["knowledge"]:
                    recommendations.append({
                        "drone_spec": result["knowledge"]["drone_spec"],
                        "relevance_score": result["score"],
                        "reason": f"匹配度: {result['score']:.2f}"
                    })
                    
            return recommendations
            
        except Exception as e:
            logger.error(f"获取无人机推荐失败: {e}")
            return []
            
    async def get_mission_patterns(
        self,
        mission_type: str,
        area_size: float
    ) -> List[Dict[str, Any]]:
        """获取任务模式建议"""
        try:
            query = f"任务类型 {mission_type} 区域大小 {area_size}"
            
            results = await self.search_knowledge(
                query=query,
                knowledge_type="mission_pattern",
                limit=5
            )
            
            patterns = []
            for result in results:
                patterns.append({
                    "pattern": result["knowledge"],
                    "relevance_score": result["score"]
                })
                
            return patterns
            
        except Exception as e:
            logger.error(f"获取任务模式建议失败: {e}")
            return []
            
    async def delete_knowledge(self, knowledge_id: str):
        """删除知识"""
        try:
            self.qdrant_client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=[knowledge_id]
                )
            )
            
            logger.info(f"删除知识: {knowledge_id}")
            
        except Exception as e:
            logger.error(f"删除知识失败: {e}")
            raise
            
    async def get_knowledge_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            collection_info = self.qdrant_client.get_collection(self.collection_name)
            
            # 按类型统计
            type_stats = {}
            scroll_result = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                limit=1000,  # 假设知识库不会太大
                with_payload=True
            )
            
            for point in scroll_result[0]:
                knowledge_type = point.payload.get("type", "unknown")
                type_stats[knowledge_type] = type_stats.get(knowledge_type, 0) + 1
                
            return {
                "total_points": collection_info.points_count,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.value,
                "by_type": type_stats
            }
            
        except Exception as e:
            logger.error(f"获取知识库统计失败: {e}")
            return {}


# 全局RAG服务实例
rag_service = RAGService()


async def get_rag_service() -> RAGService:
    """依赖注入：获取RAG服务"""
    return rag_service
