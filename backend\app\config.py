"""
应用配置管理模块
"""
from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="Mission Planning System", env="APP_NAME")
    app_version: str = Field(default="0.1.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # 数据库配置
    database_url: str = Field(env="DATABASE_URL")
    database_pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # 时间序列数据库配置
    timescale_url: str = Field(env="TIMESCALE_URL")
    
    # Redis 配置
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    redis_cache_ttl: int = Field(default=3600, env="REDIS_CACHE_TTL")
    
    # Qdrant 向量数据库配置
    qdrant_host: str = Field(default="localhost", env="QDRANT_HOST")
    qdrant_port: int = Field(default=6333, env="QDRANT_PORT")
    qdrant_api_key: Optional[str] = Field(default=None, env="QDRANT_API_KEY")
    qdrant_collection_name: str = Field(default="drone_knowledge", env="QDRANT_COLLECTION_NAME")
    
    # MinIO 对象存储配置
    minio_endpoint: str = Field(default="localhost:9000", env="MINIO_ENDPOINT")
    minio_access_key: str = Field(default="minioadmin", env="MINIO_ACCESS_KEY")
    minio_secret_key: str = Field(default="minioadmin", env="MINIO_SECRET_KEY")
    minio_bucket_name: str = Field(default="mission-data", env="MINIO_BUCKET_NAME")
    minio_secure: bool = Field(default=False, env="MINIO_SECURE")
    
    # AI 模型配置
    qwen_api_key: Optional[str] = Field(default=None, env="QWEN_API_KEY")
    qwen_api_base: str = Field(default="https://api.qwen.com/v1", env="QWEN_API_BASE")
    qwen_model: str = Field(default="qwen-turbo", env="QWEN_MODEL")
    embedding_model: str = Field(default="sentence-transformers/all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    
    # Cesium 配置
    cesium_ion_access_token: Optional[str] = Field(default=None, env="CESIUM_ION_ACCESS_TOKEN")
    cesium_terrain_provider: Optional[str] = Field(default=None, env="CESIUM_TERRAIN_PROVIDER")
    
    # gRPC 服务配置
    grpc_host: str = Field(default="localhost", env="GRPC_HOST")
    grpc_port: int = Field(default=50051, env="GRPC_PORT")
    grpc_max_workers: int = Field(default=10, env="GRPC_MAX_WORKERS")
    
    # WebSocket 配置
    websocket_ping_interval: int = Field(default=20, env="WEBSOCKET_PING_INTERVAL")
    websocket_ping_timeout: int = Field(default=10, env="WEBSOCKET_PING_TIMEOUT")
    
    # 安全配置
    secret_key: str = Field(env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    
    # CORS 配置
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        env="ALLOWED_ORIGINS"
    )
    allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        env="ALLOWED_METHODS"
    )
    allowed_headers: List[str] = Field(default=["*"], env="ALLOWED_HEADERS")
    
    # 外部 API 配置
    external_target_api_url: Optional[str] = Field(default=None, env="EXTERNAL_TARGET_API_URL")
    external_target_api_key: Optional[str] = Field(default=None, env="EXTERNAL_TARGET_API_KEY")
    
    # 监控配置
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    
    # 算法配置
    genetic_algorithm_population_size: int = Field(default=100, env="GENETIC_ALGORITHM_POPULATION_SIZE")
    genetic_algorithm_generations: int = Field(default=50, env="GENETIC_ALGORITHM_GENERATIONS")
    genetic_algorithm_mutation_rate: float = Field(default=0.1, env="GENETIC_ALGORITHM_MUTATION_RATE")
    genetic_algorithm_crossover_rate: float = Field(default=0.8, env="GENETIC_ALGORITHM_CROSSOVER_RATE")
    
    # 无人机配置
    max_drones_per_mission: int = Field(default=10, env="MAX_DRONES_PER_MISSION")
    default_drone_speed: float = Field(default=15.0, env="DEFAULT_DRONE_SPEED")  # m/s
    default_drone_altitude: float = Field(default=100.0, env="DEFAULT_DRONE_ALTITUDE")  # meters
    default_battery_life: int = Field(default=3600, env="DEFAULT_BATTERY_LIFE")  # seconds
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
