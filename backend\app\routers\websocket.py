"""
WebSocket 实时通信路由
"""
import json
import asyncio
from typing import Dict, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from loguru import logger

router = APIRouter()


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接: session_id -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        # 用户会话: user_id -> Set[session_id]
        self.user_sessions: Dict[str, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, session_id: str, user_id: str = None):
        """建立WebSocket连接"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        
        if user_id:
            if user_id not in self.user_sessions:
                self.user_sessions[user_id] = set()
            self.user_sessions[user_id].add(session_id)
            
        logger.info(f"WebSocket连接建立: session={session_id}, user={user_id}")
        
    def disconnect(self, session_id: str, user_id: str = None):
        """断开WebSocket连接"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
            
        if user_id and user_id in self.user_sessions:
            self.user_sessions[user_id].discard(session_id)
            if not self.user_sessions[user_id]:
                del self.user_sessions[user_id]
                
        logger.info(f"WebSocket连接断开: session={session_id}, user={user_id}")
        
    async def send_personal_message(self, message: dict, session_id: str):
        """发送个人消息"""
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
                self.disconnect(session_id)
                
    async def send_user_message(self, message: dict, user_id: str):
        """发送用户消息（所有会话）"""
        if user_id in self.user_sessions:
            for session_id in self.user_sessions[user_id].copy():
                await self.send_personal_message(message, session_id)
                
    async def broadcast(self, message: dict):
        """广播消息"""
        disconnected_sessions = []
        for session_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected_sessions.append(session_id)
                
        # 清理断开的连接
        for session_id in disconnected_sessions:
            self.disconnect(session_id)


# 全局连接管理器
manager = ConnectionManager()


@router.websocket("/chat/{session_id}")
async def websocket_chat(websocket: WebSocket, session_id: str, user_id: str = None):
    """聊天WebSocket端点"""
    await manager.connect(websocket, session_id, user_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理不同类型的消息
            message_type = message_data.get("type", "chat")
            
            if message_type == "chat":
                await handle_chat_message(message_data, session_id)
            elif message_type == "planning_request":
                await handle_planning_request(message_data, session_id)
            elif message_type == "mission_update":
                await handle_mission_update(message_data, session_id)
            elif message_type == "ping":
                await manager.send_personal_message(
                    {"type": "pong", "timestamp": message_data.get("timestamp")},
                    session_id
                )
            else:
                logger.warning(f"未知消息类型: {message_type}")
                
    except WebSocketDisconnect:
        manager.disconnect(session_id, user_id)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(session_id, user_id)


@router.websocket("/mission/{mission_id}")
async def websocket_mission(websocket: WebSocket, mission_id: str, session_id: str):
    """任务实时更新WebSocket端点"""
    await manager.connect(websocket, session_id)
    
    try:
        # 发送初始任务状态
        await manager.send_personal_message({
            "type": "mission_status",
            "mission_id": mission_id,
            "status": "connected",
            "message": "已连接到任务实时更新"
        }, session_id)
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理任务相关消息
            message_type = message_data.get("type", "status_request")
            
            if message_type == "status_request":
                await send_mission_status(mission_id, session_id)
            elif message_type == "drone_status_request":
                await send_drone_status(mission_id, session_id)
            elif message_type == "subscribe_updates":
                # 订阅实时更新
                await start_mission_updates(mission_id, session_id)
                
    except WebSocketDisconnect:
        manager.disconnect(session_id)
    except Exception as e:
        logger.error(f"任务WebSocket错误: {e}")
        manager.disconnect(session_id)


async def handle_chat_message(message_data: dict, session_id: str):
    """处理聊天消息"""
    try:
        user_message = message_data.get("message", "")
        conversation_id = message_data.get("conversation_id", session_id)
        
        # TODO: 集成AI模型处理用户消息
        # 这里先返回模拟响应
        
        response = {
            "type": "chat_response",
            "conversation_id": conversation_id,
            "message": f"收到您的消息：{user_message}。正在分析...",
            "timestamp": "2025-08-08T09:19:00Z"
        }
        
        await manager.send_personal_message(response, session_id)
        
        # 模拟AI处理延迟
        await asyncio.sleep(1)
        
        ai_response = {
            "type": "ai_response",
            "conversation_id": conversation_id,
            "message": "基于您的需求，我建议使用3架无人机进行协同侦察。请提供目标区域坐标。",
            "suggestions": [
                "设置侦察区域",
                "选择无人机类型",
                "配置飞行参数"
            ],
            "timestamp": "2025-08-08T09:19:01Z"
        }
        
        await manager.send_personal_message(ai_response, session_id)
        
    except Exception as e:
        logger.error(f"处理聊天消息失败: {e}")


async def handle_planning_request(message_data: dict, session_id: str):
    """处理规划请求"""
    try:
        # TODO: 调用AI规划服务
        
        response = {
            "type": "planning_response",
            "status": "processing",
            "message": "正在生成任务规划方案...",
            "progress": 0,
            "timestamp": "2025-08-08T09:19:00Z"
        }
        
        await manager.send_personal_message(response, session_id)
        
        # 模拟规划过程
        for progress in [25, 50, 75, 100]:
            await asyncio.sleep(0.5)
            response["progress"] = progress
            response["message"] = f"规划进度: {progress}%"
            await manager.send_personal_message(response, session_id)
            
        # 发送最终结果
        final_response = {
            "type": "planning_complete",
            "mission_id": "mission_123",
            "message": "任务规划完成",
            "summary": {
                "drones": 3,
                "waypoints": 25,
                "estimated_time": "45分钟",
                "coverage": "95%"
            },
            "timestamp": "2025-08-08T09:19:02Z"
        }
        
        await manager.send_personal_message(final_response, session_id)
        
    except Exception as e:
        logger.error(f"处理规划请求失败: {e}")


async def handle_mission_update(message_data: dict, session_id: str):
    """处理任务更新"""
    try:
        mission_id = message_data.get("mission_id")
        update_type = message_data.get("update_type", "status")
        
        response = {
            "type": "mission_updated",
            "mission_id": mission_id,
            "update_type": update_type,
            "message": "任务状态已更新",
            "timestamp": "2025-08-08T09:19:00Z"
        }
        
        await manager.send_personal_message(response, session_id)
        
    except Exception as e:
        logger.error(f"处理任务更新失败: {e}")


async def send_mission_status(mission_id: str, session_id: str):
    """发送任务状态"""
    try:
        # TODO: 从数据库获取真实任务状态
        
        status = {
            "type": "mission_status",
            "mission_id": mission_id,
            "status": "ACTIVE",
            "progress": 65,
            "drones": [
                {"id": "drone_1", "status": "FLYING", "battery": 0.75},
                {"id": "drone_2", "status": "FLYING", "battery": 0.68},
                {"id": "drone_3", "status": "CHARGING", "battery": 0.95}
            ],
            "timestamp": "2025-08-08T09:19:00Z"
        }
        
        await manager.send_personal_message(status, session_id)
        
    except Exception as e:
        logger.error(f"发送任务状态失败: {e}")


async def send_drone_status(mission_id: str, session_id: str):
    """发送无人机状态"""
    try:
        # TODO: 从数据库获取真实无人机状态
        
        drone_status = {
            "type": "drone_status",
            "mission_id": mission_id,
            "drones": [
                {
                    "id": "drone_1",
                    "position": {"lat": 39.9042, "lng": 116.4074, "alt": 100},
                    "speed": 15.5,
                    "heading": 45,
                    "battery": 0.75,
                    "status": "FLYING"
                },
                {
                    "id": "drone_2",
                    "position": {"lat": 39.9052, "lng": 116.4084, "alt": 120},
                    "speed": 12.3,
                    "heading": 90,
                    "battery": 0.68,
                    "status": "FLYING"
                }
            ],
            "timestamp": "2025-08-08T09:19:00Z"
        }
        
        await manager.send_personal_message(drone_status, session_id)
        
    except Exception as e:
        logger.error(f"发送无人机状态失败: {e}")


async def start_mission_updates(mission_id: str, session_id: str):
    """开始任务实时更新"""
    try:
        # TODO: 实现真实的实时更新逻辑
        
        response = {
            "type": "subscription_started",
            "mission_id": mission_id,
            "message": "已开始实时更新订阅",
            "update_interval": "5秒",
            "timestamp": "2025-08-08T09:19:00Z"
        }
        
        await manager.send_personal_message(response, session_id)
        
    except Exception as e:
        logger.error(f"开始任务更新失败: {e}")


# 用于外部调用的辅助函数
async def broadcast_mission_update(mission_id: str, update_data: dict):
    """广播任务更新"""
    message = {
        "type": "mission_broadcast",
        "mission_id": mission_id,
        "data": update_data,
        "timestamp": "2025-08-08T09:19:00Z"
    }
    await manager.broadcast(message)


async def send_drone_position_update(drone_id: str, position: dict):
    """发送无人机位置更新"""
    message = {
        "type": "drone_position_update",
        "drone_id": drone_id,
        "position": position,
        "timestamp": "2025-08-08T09:19:00Z"
    }
    await manager.broadcast(message)
