"""
任务管理路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from loguru import logger

from app.models import Mission, MissionDB, MissionStatusEnum
from database.connection import get_pg_session

router = APIRouter()


@router.post("/", response_model=Mission)
async def create_mission(
    mission: Mission,
    db: AsyncSession = Depends(get_pg_session)
):
    """创建新任务"""
    try:
        # 转换为数据库模型
        mission_db = MissionDB(
            id=mission.id,
            name=mission.name,
            description=mission.description,
            target_ids=mission.target_ids,
            flight_paths=[fp.dict() for fp in mission.flight_paths],
            status=mission.status.value,
            area_center_lat=mission.area_center.latitude,
            area_center_lon=mission.area_center.longitude,
            area_center_alt=mission.area_center.altitude,
            area_radius=mission.area_radius,
            parameters=mission.parameters,
            created_at=mission.created_at,
            started_at=mission.started_at,
            completed_at=mission.completed_at
        )
        
        db.add(mission_db)
        await db.commit()
        await db.refresh(mission_db)
        
        logger.info(f"创建任务成功: {mission.id}")
        return mission
        
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        raise HTTPException(status_code=500, detail="创建任务失败")


@router.get("/", response_model=List[Mission])
async def list_missions(
    status: Optional[MissionStatusEnum] = Query(None, description="状态过滤"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_pg_session)
):
    """获取任务列表"""
    try:
        query = select(MissionDB)
        
        if status:
            query = query.where(MissionDB.status == status.value)
            
        # 分页
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        result = await db.execute(query)
        missions_db = result.scalars().all()
        
        # 转换为Pydantic模型
        missions = []
        for mission_db in missions_db:
            mission = Mission(
                id=mission_db.id,
                name=mission_db.name,
                description=mission_db.description,
                target_ids=mission_db.target_ids or [],
                flight_paths=[],  # 需要单独处理
                status=MissionStatusEnum(mission_db.status),
                area_center={
                    "latitude": mission_db.area_center_lat,
                    "longitude": mission_db.area_center_lon,
                    "altitude": mission_db.area_center_alt
                },
                area_radius=mission_db.area_radius,
                created_at=mission_db.created_at,
                started_at=mission_db.started_at,
                completed_at=mission_db.completed_at,
                parameters=mission_db.parameters or {}
            )
            missions.append(mission)
            
        return missions
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务列表失败")


@router.get("/{mission_id}", response_model=Mission)
async def get_mission(
    mission_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """获取单个任务详情"""
    try:
        query = select(MissionDB).where(MissionDB.id == mission_id)
        result = await db.execute(query)
        mission_db = result.scalar_one_or_none()
        
        if not mission_db:
            raise HTTPException(status_code=404, detail="任务不存在")
            
        # 转换为Pydantic模型
        mission = Mission(
            id=mission_db.id,
            name=mission_db.name,
            description=mission_db.description,
            target_ids=mission_db.target_ids or [],
            flight_paths=[],  # 需要单独处理
            status=MissionStatusEnum(mission_db.status),
            area_center={
                "latitude": mission_db.area_center_lat,
                "longitude": mission_db.area_center_lon,
                "altitude": mission_db.area_center_alt
            },
            area_radius=mission_db.area_radius,
            created_at=mission_db.created_at,
            started_at=mission_db.started_at,
            completed_at=mission_db.completed_at,
            parameters=mission_db.parameters or {}
        )
        
        return mission
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取任务详情失败")


@router.put("/{mission_id}", response_model=Mission)
async def update_mission(
    mission_id: str,
    mission: Mission,
    db: AsyncSession = Depends(get_pg_session)
):
    """更新任务"""
    try:
        # 检查任务是否存在
        query = select(MissionDB).where(MissionDB.id == mission_id)
        result = await db.execute(query)
        existing_mission = result.scalar_one_or_none()
        
        if not existing_mission:
            raise HTTPException(status_code=404, detail="任务不存在")
            
        # 更新任务
        update_query = update(MissionDB).where(MissionDB.id == mission_id).values(
            name=mission.name,
            description=mission.description,
            target_ids=mission.target_ids,
            flight_paths=[fp.dict() for fp in mission.flight_paths],
            status=mission.status.value,
            area_center_lat=mission.area_center.latitude,
            area_center_lon=mission.area_center.longitude,
            area_center_alt=mission.area_center.altitude,
            area_radius=mission.area_radius,
            parameters=mission.parameters,
            started_at=mission.started_at,
            completed_at=mission.completed_at
        )
        
        await db.execute(update_query)
        await db.commit()
        
        logger.info(f"更新任务成功: {mission_id}")
        return mission
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新任务失败: {e}")
        raise HTTPException(status_code=500, detail="更新任务失败")


@router.delete("/{mission_id}")
async def delete_mission(
    mission_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """删除任务"""
    try:
        # 检查任务是否存在
        query = select(MissionDB).where(MissionDB.id == mission_id)
        result = await db.execute(query)
        existing_mission = result.scalar_one_or_none()
        
        if not existing_mission:
            raise HTTPException(status_code=404, detail="任务不存在")
            
        # 删除任务
        delete_query = delete(MissionDB).where(MissionDB.id == mission_id)
        await db.execute(delete_query)
        await db.commit()
        
        logger.info(f"删除任务成功: {mission_id}")
        return {"message": "任务删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(status_code=500, detail="删除任务失败")


@router.post("/{mission_id}/start")
async def start_mission(
    mission_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """启动任务"""
    try:
        # 检查任务是否存在
        query = select(MissionDB).where(MissionDB.id == mission_id)
        result = await db.execute(query)
        existing_mission = result.scalar_one_or_none()
        
        if not existing_mission:
            raise HTTPException(status_code=404, detail="任务不存在")
            
        if existing_mission.status != MissionStatusEnum.PLANNING.value:
            raise HTTPException(status_code=400, detail="只能启动规划中的任务")
            
        # 启动任务
        from datetime import datetime
        update_query = update(MissionDB).where(MissionDB.id == mission_id).values(
            status=MissionStatusEnum.ACTIVE.value,
            started_at=datetime.utcnow()
        )
        
        await db.execute(update_query)
        await db.commit()
        
        logger.info(f"启动任务成功: {mission_id}")
        return {"message": "任务启动成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动任务失败: {e}")
        raise HTTPException(status_code=500, detail="启动任务失败")


@router.post("/{mission_id}/complete")
async def complete_mission(
    mission_id: str,
    db: AsyncSession = Depends(get_pg_session)
):
    """完成任务"""
    try:
        # 检查任务是否存在
        query = select(MissionDB).where(MissionDB.id == mission_id)
        result = await db.execute(query)
        existing_mission = result.scalar_one_or_none()
        
        if not existing_mission:
            raise HTTPException(status_code=404, detail="任务不存在")
            
        if existing_mission.status != MissionStatusEnum.ACTIVE.value:
            raise HTTPException(status_code=400, detail="只能完成活跃的任务")
            
        # 完成任务
        from datetime import datetime
        update_query = update(MissionDB).where(MissionDB.id == mission_id).values(
            status=MissionStatusEnum.COMPLETED.value,
            completed_at=datetime.utcnow()
        )
        
        await db.execute(update_query)
        await db.commit()
        
        logger.info(f"完成任务成功: {mission_id}")
        return {"message": "任务完成成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"完成任务失败: {e}")
        raise HTTPException(status_code=500, detail="完成任务失败")
